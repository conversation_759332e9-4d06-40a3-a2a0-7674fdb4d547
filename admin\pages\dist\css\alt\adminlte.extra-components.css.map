{"version": 3, "sources": ["../../../build/scss/parts/adminlte.extra-components.scss", "adminlte.extra-components.css", "../../../build/scss/mixins/_animations.scss", "../../../build/scss/_small-box.scss", "../../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../../build/scss/_bootstrap-variables.scss", "../../../node_modules/bootstrap/scss/mixins/_box-shadow.scss", "../../../build/scss/_variables.scss", "../../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "../../../build/scss/_info-box.scss", "../../../build/scss/_timeline.scss", "../../../build/scss/_products.scss", "../../../node_modules/bootstrap/scss/mixins/_clearfix.scss", "../../../build/scss/_direct-chat.scss", "../../../build/scss/mixins/_miscellaneous.scss", "../../../node_modules/bootstrap/scss/mixins/_lists.scss", "../../../build/scss/mixins/_direct-chat.scss", "../../../build/scss/_variables-alt.scss", "../../../build/scss/_users-list.scss", "../../../build/scss/_social-widgets.scss"], "names": [], "mappings": "AAAA;;;;;;ECME;ACDF;EACE;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,mCAAmC;IACnC,UAAU;EDGZ;ECAA;IACE,+DAAuD;IAAvD,uDAAuD;IACvD,mCAAmC;EDErC;ECCA;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,UAAU;EDCZ;ECEA;IACE,8DAAsD;IAAtD,sDAAsD;EDAxD;ECGA;IACE,qCAA6B;IAA7B,6BAA6B;EDD/B;AACF;ACtBA;EACE;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,mCAAmC;IACnC,UAAU;EDGZ;ECAA;IACE,+DAAuD;IAAvD,uDAAuD;IACvD,mCAAmC;EDErC;ECCA;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,UAAU;EDCZ;ECEA;IACE,8DAAsD;IAAtD,sDAAsD;EDAxD;ECGA;IACE,qCAA6B;IAA7B,6BAA6B;EDD/B;AACF;;ACKA;EACE;IACE,UAAU;EDFZ;ECKA;IACE,UAAU;EDHZ;AACF;;ACJA;EACE;IACE,UAAU;EDFZ;ECKA;IACE,UAAU;EDHZ;AACF;;ACMA;EACE;IACE,UAAU;EDHZ;ECMA;IACE,UAAU;EDJZ;AACF;;ACHA;EACE;IACE,UAAU;EDHZ;ECMA;IACE,UAAU;EDJZ;AACF;;ACOA;EACE;IACE,mDAA2C;IAA3C,2CAA2C;EDJ7C;ECMA;IACE,sDAA8C;IAA9C,8CAA8C;EDJhD;ECMA;IACE,kDAA0C;IAA1C,0CAA0C;EDJ5C;ECMA;IACE,iDAAyC;IAAzC,yCAAyC;EDJ3C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;ECMA;IACE,mDAA2C;IAA3C,2CAA2C;EDJ7C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;AACF;;AC7BA;EACE;IACE,mDAA2C;IAA3C,2CAA2C;EDJ7C;ECMA;IACE,sDAA8C;IAA9C,8CAA8C;EDJhD;ECMA;IACE,kDAA0C;IAA1C,0CAA0C;EDJ5C;ECMA;IACE,iDAAyC;IAAzC,yCAAyC;EDJ3C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,oDAA4C;IAA5C,4CAA4C;EDJ9C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;ECMA;IACE,mDAA2C;IAA3C,2CAA2C;EDJ7C;ECMA;IACE,qDAA6C;IAA7C,6CAA6C;EDJ/C;AACF;;ACOA;EACE;IACE,uBAAe;IAAf,eAAe;EDJjB;ECOA;IACE,mEAA2D;IAA3D,2DAA2D;EDL7D;ECQA;IACE,iEAAyD;IAAzD,yDAAyD;EDN3D;ECSA;IACE,mEAA2D;IAA3D,2DAA2D;EDP7D;ECUA;IACE,iEAAyD;IAAzD,yDAAyD;EDR3D;ECWA;IACE,kEAA0D;IAA1D,0DAA0D;EDT5D;ECYA;IACE,uBAAe;IAAf,eAAe;EDVjB;AACF;;ACjBA;EACE;IACE,uBAAe;IAAf,eAAe;EDJjB;ECOA;IACE,mEAA2D;IAA3D,2DAA2D;EDL7D;ECQA;IACE,iEAAyD;IAAzD,yDAAyD;EDN3D;ECSA;IACE,mEAA2D;IAA3D,2DAA2D;EDP7D;ECUA;IACE,iEAAyD;IAAzD,yDAAyD;EDR3D;ECWA;IACE,kEAA0D;IAA1D,0DAA0D;EDT5D;ECYA;IACE,uBAAe;IAAf,eAAe;EDVjB;AACF;;AErGA;ECcI,sBCmMgC;ECrM9B,sECsIgE;EJ9IpE,cAAc;EACd,mBAAmB;EACnB,kBAAkB;AFuGpB;;AE7GA;EAUI,aAAa;AFuGjB;;AEjHA;EAcI,oCEGW;EFFX,+BERW;EFSX,cAAc;EACd,cAAc;EACd,kBAAkB;EAClB,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;AFuGf;;AE5HA;EAwBM,qCEPS;EFQT,WElBS;AJ0Hf;;AEjIA;EK2HM,iBAtCY;ELtDd,gBAAgB;EAChB,gBAAgB;EAChB,UAAU;EACV,mBAAmB;AFuGvB;;AQjFI;ENlBA;;;IKqFE,iBAtCY;EP2DhB;EElGE;;;IK6EE,iBAtCY;EPgEhB;AACF;;AQ9FI;ENAA;;;IKmEE,iBAtCY;EPwEhB;EE7FE;;;IK2DE,iBAtCY;EP6EhB;AACF;;AEnKA;EA0EI,eAAe;AF6FnB;;AEvKA;EA6EM,cErEY;EFsEZ,cAAc;EACd,gBAAgB;EAChB,eAAe;AF8FrB;;AE9KA;;EAsFI,UAAU;AF6Fd;;AEnLA;EA2FI,0BE1EW;EF2EX,UAAU;AF4Fd;;AExLA;EA+FM,eAAe;EACf,kBAAkB;EAClB,WAAW;EACX,SAAS;EACT,yCAA8C;EAA9C,iCAA8C;EAA9C,gEAA8C;AF6FpD;;AEhMA;EA4GQ,eAAe;EACf,SAAS;AFwFjB;;AErMA;EAkHM,eAAe;EACf,kBAAkB;EAClB,WAAW;EACX,SAAS;EACT,yCAA8C;EAA9C,iCAA8C;EAA9C,gEAA8C;AFuFpD;;AE7MA;EA4HI,qBAAqB;AFqFzB;;AEjNA;EAyIU,6BAAqB;EAArB,qBAAqB;AF4E/B;;AErNA;EA6IQ,6BAAqB;EAArB,qBAAqB;AF4E7B;;AQpJI;ENgFF;IACE,kBAAkB;EFwEpB;EEzEA;IAII,aAAa;EFwEjB;EE5EA;IAQI,eAAe;EFuEnB;AACF;;ASrOA;EJYM,sECsIgE;EHpIlE,sBCmMgC;EK7MlC,sBLGa;EKFb,qBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,mBL+GW;EK9GX,gBAAgB;EAChB,cAAc;EACd,kBAAkB;EAClB,WAAW;ATuOb;;ASjPA;EAaI,sCLIW;EKHX,WAAW;EACX,aAAa;ATwOjB;;ASvPA;EAkBM,sBLXS;AJoPf;;AS3PA;EAwBM,sBLyL8B;EKtLhC,2BAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EACnB,qBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,mBAAmB;EACnB,+BAAuB;EAAvB,qBAAuB;EAAvB,uBAAuB;EACvB,kBAAkB;EAClB,WAAW;ATqOf;;ASrQA;EAmCM,eAAe;ATsOrB;;ASzQA;EAwCI,qBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,8BAAsB;EAAtB,0BAAsB;EAAtB,sBAAsB;EACtB,+BAAuB;EAAvB,qBAAuB;EAAvB,uBAAuB;EACvB,gBAAgB;EAChB,eAAO;EAAP,WAAO;EAAP,OAAO;EACP,eAAe;ATqOnB;;ASlRA;EAiDI,cAAc;EACd,kBAAkB;EAClB,gBL4L6B;AJyCjC;;ASxRA;;EAwDI,cAAc;EACd,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;ATqOvB;;AShSA;;EAkEQ,WL3DO;AJ8Rf;;ASrSA;;EAqEU,sBL9DK;AJmSf;;AS1SA;;EAkEQ,WL3DO;AJwSf;;AS/SA;;EAqEU,sBL9DK;AJ6Sf;;ASpTA;;EAkEQ,WL3DO;AJkTf;;ASzTA;;EAqEU,sBL9DK;AJuTf;;AS9TA;;EAkEQ,WL3DO;AJ4Tf;;ASnUA;;EAqEU,sBL9DK;AJiUf;;ASxUA;;EAkEQ,cLuBe;AJoPvB;;AS7UA;;EAqEU,yBLoBa;AJyPvB;;ASlVA;;EAkEQ,WL3DO;AJgVf;;ASvVA;;EAqEU,sBL9DK;AJqVf;;AS5VA;;EAkEQ,cLuBe;AJwQvB;;ASjWA;;EAqEU,yBLoBa;AJ6QvB;;AStWA;;EAkEQ,WL3DO;AJoWf;;AS3WA;;EAqEU,sBL9DK;AJyWf;;AShXA;EA4EI,cAAc;ATwSlB;;ASpXA;EAgFI,SAAS;ATwSb;;AQhUI;EC6BA;;;IAII,aAAa;ETsSnB;ESlSE;;;IAII,aAAa;ETmSnB;AACF;;AQ7UI;EC+CA;;;IFoBE,kBAtCY;IEuBV,cAAc;ETiSpB;ES7RE;;;IFWE,kBAtCY;IEgCV,cAAc;ET8RpB;AACF;;AQ5VI;ECmEA;;;IFAE,eAtCY;IE2CV,cAAc;ET4RpB;ESxRE;;;IFTE,eAtCY;IEoDV,cAAc;ETyRpB;AACF;;ASpRA;EAEI,yBLlIc;EKmId,WL3IW;AJiaf;;ASzRA;;EAQU,WLhJK;AJsaf;;AS9RA;;EAWY,sBLnJG;AJ2af;;ASnSA;;EAQU,WLhJK;AJgbf;;ASxSA;;EAWY,sBLnJG;AJqbf;;AS7SA;;EAQU,WLhJK;AJ0bf;;ASlTA;;EAWY,sBLnJG;AJ+bf;;ASvTA;;EAQU,WLhJK;AJocf;;AS5TA;;EAWY,sBLnJG;AJycf;;ASjUA;;EAQU,cL9Da;AJ4XvB;;AStUA;;EAWY,yBLjEW;AJiYvB;;AS3UA;;EAQU,WLhJK;AJwdf;;AShVA;;EAWY,sBLnJG;AJ6df;;ASrVA;;EAQU,cL9Da;AJgZvB;;AS1VA;;EAWY,yBLjEW;AJqZvB;;AS/VA;;EAQU,WLhJK;AJ4ef;;ASpWA;;EAWY,sBLnJG;AJiff;;AUxfA;EACE,gBAAgB;EAChB,UAAU;EACV,kBAAkB;AV2fpB;;AU9fA;EPcI,sBCmMgC;EM1MhC,yBNGc;EMFd,SAAS;EACT,WAAW;EACX,UAAU;EACV,SAAS;EACT,kBAAkB;EAClB,MAAM;EACN,UAAU;AV4fd;;AU1gBA;EAwBI,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;AVsftB;;AUhhBA;EAoBM,WAAW;EACX,cAAc;AVggBpB;;AUrhBA;ELYM,sECsIgE;EHpIlE,sBCmMgC;EMlL9B,sBNxBS;EMyBT,cNlBY;EMmBZ,iBAAiB;EACjB,kBAAkB;EAClB,aAAa;EACb,UAAU;EACV,kBAAkB;AV4fxB;;AUjiBA;EAwCQ,WAAW;EACX,YAAY;EACZ,eAAe;EACf,aAAa;AV6frB;;AUxiBA;EA+CQ,6CN9BO;EM+BP,cNlCU;EMmCV,eAAe;EACf,gBAAgB;EAChB,SAAS;EACT,aAAa;AV6frB;;AUjjBA;EAuDU,gBAAgB;AV8f1B;;AUrjBA;;EA6DQ,aAAa;AV6frB;;AU1jBA;EAkEU,YAAY;AV4ftB;;AU9jBA;;;EAuEU,SAAS;AV6fnB;;AUpkBA;EA6EU,WNtEK;AJikBf;;AUxkBA;;;;;;;;EA0FM,yBN9EY;EM+EZ,kBAAkB;EAClB,eAAe;EACf,YAAY;EACZ,UAAU;EACV,iBAAiB;EACjB,kBAAkB;EAClB,kBAAkB;EAClB,MAAM;EACN,WAAW;AVyfjB;;AU5lBA;EAsGM,YAAY;AV0flB;;AUhmBA;EPcI,kBO8F4B;EAC1B,sBNtGS;EMuGT,qBAAqB;EACrB,gBAAgB;EAChB,YAAY;AVwflB;;AUnfA;ELzGM,gBK4GwB;EACxB,yBNjHY;EMkHZ,yBNhHY;AJomBlB;;AUzfA;EAQQ,4BNnHU;AJwmBlB;;AU/eA;EAGM,yBNzHY;AJymBlB;;AUnfA;EAMM,yBN1HY;EM2HZ,WNnIS;EMoIT,qBN9HY;AJ+mBlB;;AUzfA;EAWQ,cNnIU;EMoIV,qBNlIU;AJonBlB;;AU9fA;EAeQ,cNvIU;AJ0nBlB;;AWroBA;EACE,gBAAgB;EAChB,SAAS;EACT,UAAU;AXwoBZ;;AW3oBA;ERcI,sBCmMgC;EOrMhC,sBPLW;EOMX,eAAe;AXooBnB;;AYppBE;EACE,cAAc;EACd,WAAW;EACX,WAAW;AZupBf;;AWvpBA;EAkBI,WAAW;AXyoBf;;AW3pBA;EAqBM,YAAY;EACZ,WAAW;AX0oBjB;;AWhqBA;EA2BI,iBAAiB;AXyoBrB;;AWpqBA;EA+BI,gBAAgB;AXyoBpB;;AWxqBA;EAmCI,cPtBc;EOuBd,cAAc;EACd,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;AXyoBvB;;AWroBA;ER7BI,gBQ8BsB;EACxB,6CP5Ba;AJoqBf;;AW1oBA;EAKI,sBAAsB;AXyoB1B;;AWpoBA;EAEI,yBPxCc;EOyCd,WPjDW;EOkDX,4BP5Cc;AJkrBlB;;AW1oBA;EAQI,cPlDc;AJwrBlB;;AansBA;EAEI,kBAAkB;EAClB,UAAU;EACV,kBAAkB;AbqsBtB;;AazsBA;ECEE,kCAA4B;EAA5B,0BAA4B;Ad2sB9B;;Aa7sBA;EAgBM,cAA+C;AbisBrD;;AajtBA;EAsBM,cAA6C;Ab+rBnD;;Aa1rBA;ECzBE,kCAA4B;EAA5B,0BAA4B;ED2B5B,aAAa;EACb,cAAc;EACd,aAAa;Ab6rBf;;Aa1rBA;;EAEE,cAAc;Ab6rBhB;;Aa1rBA;EAEE,mBAAmB;Ab4rBrB;;AYxuBE;EACE,cAAc;EACd,WAAW;EACX,WAAW;AZ2uBf;;Aa/rBA;;EAEE,6CAAqC;EAArC,qCAAqC;EAArC,wEAAqC;AbksBvC;;Aa/rBA;EVnCI,qBCoM+B;ES5JjC,yBPzCoB;EO0CpB,yBP1CoB;EO2CpB,WPyGmC;EOxGnC,oBAAoB;EACpB,iBAAiB;EACjB,kBAAkB;Ab+rBpB;;AazsBA;EAeI,yBAAyB;EACzB,2BPpDkB;EOqDlB,YAAY;EACZ,SAAS;EACT,oBAAoB;EACpB,kBAAkB;EAClB,WAAW;EACX,SAAS;EACT,QAAQ;Ab8rBZ;;AartBA;EA2BI,iBAAiB;EACjB,gBAAgB;Ab8rBpB;;Aa1tBA;EAgCI,iBAAiB;EACjB,gBAAgB;Ab8rBpB;;Aa3rBE;EACE,cAAc;EACd,kBAAkB;Ab8rBtB;;AahsBE;EAMI,0BP9EgB;EO+EhB,+BAA+B;EAC/B,UAAU;EACV,WAAW;Ab8rBjB;;AazrBA;EVrFI,kBUsFwB;EAC1B,WAAW;EACX,YAAY;EACZ,WAAW;Ab4rBb;;Aa1rBE;EACE,YAAY;Ab6rBhB;;AazrBA;EACE,cAAc;EACd,mBT2HoD;ES1HpD,kBAAkB;Ab4rBpB;;AazrBA;EACE,gBAAgB;Ab4rBlB;;AazrBA;EACE,cAA6B;Ab4rB/B;;AaxrBA;EC3HE,kCAA4B;EAA5B,0BAA4B;AduzB9B;;AatrBA;ECjIE,qCAA4B;EAA5B,6BAA4B;EDmI5B,yBTtHgB;ESuHhB,SAAS;EACT,WThIa;ESiIb,aAAa;EACb,cAAc;EACd,kBAAkB;EAClB,MAAM;EACN,WAAW;AbyrBb;;AatrBA;EACE,yBTxIgB;AJi0BlB;;Aa1rBA;EAII,cTrIc;AJ+zBlB;;Aa9rBA;EAQI,cT1Ic;AJo0BlB;;AalsBA;EAYI,cAA6B;Ab0rBjC;;AarrBA;EEhKE,eAAe;EACf,gBAAgB;Afy1BlB;;Aa1rBA;EAKI,2CTpJW;ESqJX,SAAS;EACT,aAAa;AbyrBjB;;AYn2BE;EACE,cAAc;EACd,WAAW;EACX,WAAW;AZs2Bf;;AatsBA;EAUM,gBAAgB;AbgsBtB;;Aa3rBA;EVjKI,kBUkKwB;EAC1B,WAAW;EACX,WAAW;Ab8rBb;;Aa3rBA;EACE,WT/Ka;ESgLb,iBAAiB;Ab8rBnB;;Aa3rBA;;EAEE,cAAc;Ab8rBhB;;Aa3rBA;EACE,gBAAgB;Ab8rBlB;;Aa3rBA;EACE,mBTuCoD;AJupBtD;;Aa3rBA;EACE,cT7LgB;ES8LhB,gBAAgB;Ab8rBlB;;Aa3rBA;EACE,cAA6B;Ab8rB/B;;AgBz4BE;EACE,yBZ6Ba;EY5Bb,qBZ4Ba;EY3Bb,WZEW;AJ04Bf;;AgB14BI;EAEE,0BZuBW;AJq3BjB;;AgBn5BE;EACE,yBZUc;EYTd,qBZSc;EYRd,WZEW;AJo5Bf;;AgBp5BI;EAEE,0BZIY;AJk5BlB;;AgB75BE;EACE,yBZoCa;EYnCb,qBZmCa;EYlCb,WZEW;AJ85Bf;;AgB95BI;EAEE,0BZ8BW;AJk4BjB;;AgBv6BE;EACE,yBZsCa;EYrCb,qBZqCa;EYpCb,WZEW;AJw6Bf;;AgBx6BI;EAEE,0BZgCW;AJ04BjB;;AgBj7BE;EACE,yBZmCa;EYlCb,qBZkCa;EYjCb,cZoFmB;AJg2BvB;;AgBl7BI;EAEE,0BZ6BW;AJu5BjB;;AgB37BE;EACE,yBZiCa;EYhCb,qBZgCa;EY/Bb,WZEW;AJ47Bf;;AgB57BI;EAEE,0BZ2BW;AJm6BjB;;AgBr8BE;EACE,yBZKc;EYJd,qBZIc;EYHd,cZoFmB;AJo3BvB;;AgBt8BI;EAEE,0BZDY;AJy8BlB;;AgB/8BE;EACE,yBZYc;EYXd,qBZWc;EYVd,WZEW;AJg9Bf;;AgBh9BI;EAEE,0BZMY;AJ48BlB;;AgBz9BE;EACE,yBVAe;EUCf,qBVDe;EUEf,WZEW;AJ09Bf;;AgB19BI;EAEE,0BVNa;ANk+BnB;;AgBn+BE;EACE,yBVCU;EUAV,qBVAU;EUCV,WZEW;AJo+Bf;;AgBp+BI;EAEE,0BVLQ;AN2+Bd;;AgB7+BE;EACE,yBVGW;EUFX,qBVEW;EUDX,WZEW;AJ8+Bf;;AgB9+BI;EAEE,0BVHS;ANm/Bf;;AgBv/BE;EACE,yBVIU;EUHV,qBVGU;EUFV,cZoFmB;AJs6BvB;;AgBx/BI;EAEE,0BVFQ;AN4/Bd;;AgBjgCE;EACE,yBVMa;EULb,qBVKa;EUJb,WZEW;AJkgCf;;AgBlgCI;EAEE,0BVAW;ANogCjB;;AgB3gCE;EACE,yBVQY;EUPZ,qBVOY;EUNZ,WZEW;AJ4gCf;;AgB5gCI;EAEE,0BVEU;AN4gChB;;AgBrhCE;EACE,yBZ6Ba;EY5Bb,qBZ4Ba;EY3Bb,WZEW;AJshCf;;AgBthCI;EAEE,0BZuBW;AJigCjB;;AgB/hCE;EACE,yBZ8Ba;EY7Bb,qBZ6Ba;EY5Bb,WZEW;AJgiCf;;AgBhiCI;EAEE,0BZwBW;AJ0gCjB;;AgBziCE;EACE,yBZ+Ba;EY9Bb,qBZ8Ba;EY7Bb,WZEW;AJ0iCf;;AgB1iCI;EAEE,0BZyBW;AJmhCjB;;AgBnjCE;EACE,yBZgCa;EY/Bb,qBZ+Ba;EY9Bb,WZEW;AJojCf;;AgBpjCI;EAEE,0BZ0BW;AJ4hCjB;;AgB7jCE;EACE,yBZiCa;EYhCb,qBZgCa;EY/Bb,WZEW;AJ8jCf;;AgB9jCI;EAEE,0BZ2BW;AJqiCjB;;AgBvkCE;EACE,yBZkCa;EYjCb,qBZiCa;EYhCb,cZoFmB;AJs/BvB;;AgBxkCI;EAEE,0BZ4BW;AJ8iCjB;;AgBjlCE;EACE,yBZmCa;EYlCb,qBZkCa;EYjCb,cZoFmB;AJggCvB;;AgBllCI;EAEE,0BZ6BW;AJujCjB;;AgB3lCE;EACE,yBZoCa;EYnCb,qBZmCa;EYlCb,WZEW;AJ4lCf;;AgB5lCI;EAEE,0BZ8BW;AJgkCjB;;AgBrmCE;EACE,yBZqCa;EYpCb,qBZoCa;EYnCb,WZEW;AJsmCf;;AgBtmCI;EAEE,0BZ+BW;AJykCjB;;AgB/mCE;EACE,yBZsCa;EYrCb,qBZqCa;EYpCb,WZEW;AJgnCf;;AgBhnCI;EAEE,0BZgCW;AJklCjB;;AgBznCE;EACE,sBZIW;EYHX,kBZGW;EYFX,cZoFmB;AJwiCvB;;AgB1nCI;EAEE,uBZFS;AJ8nCf;;AgBnoCE;EACE,yBZUc;EYTd,qBZSc;EYRd,WZEW;AJooCf;;AgBpoCI;EAEE,0BZIY;AJkoClB;;AgB7oCE;EACE,yBZYc;EYXd,qBZWc;EYVd,WZEW;AJ8oCf;;AgB9oCI;EAEE,0BZMY;AJ0oClB;;Aa57BA;EAEI,yBAAsC;EACtC,qBAAiC;EACjC,WT1NW;AJwpCf;;Aal8BA;EAQM,2BAAuC;Ab87B7C;;Aat8BA;EAYI,cT7Nc;AJ2pClB;;Aa18BA;EAiBM,+BAA+B;Ab67BrC;;AgBzqCE;EACE,yBC4BiB;ED3BjB,qBC2BiB;ED1BjB,WZEW;AJ0qCf;;AgB1qCI;EAEE,0BCsBe;AjBspCrB;;AgBnrCE;EACE,yBZUc;EYTd,qBZSc;EYRd,WZEW;AJorCf;;AgBprCI;EAEE,0BZIY;AJkrClB;;AgB7rCE;EACE,yBCmCiB;EDlCjB,qBCkCiB;EDjCjB,WZEW;AJ8rCf;;AgB9rCI;EAEE,0BC6Be;AjBmqCrB;;AgBvsCE;EACE,yBCqCiB;EDpCjB,qBCoCiB;EDnCjB,WZEW;AJwsCf;;AgBxsCI;EAEE,0BC+Be;AjB2qCrB;;AgBjtCE;EACE,yBCkCiB;EDjCjB,qBCiCiB;EDhCjB,cZoFmB;AJgoCvB;;AgBltCI;EAEE,0BC4Be;AjBwrCrB;;AgB3tCE;EACE,yBCgCiB;ED/BjB,qBC+BiB;ED9BjB,WZEW;AJ4tCf;;AgB5tCI;EAEE,0BC0Be;AjBosCrB;;AgBruCE;EACE,yBZKc;EYJd,qBZIc;EYHd,cZoFmB;AJopCvB;;AgBtuCI;EAEE,0BZDY;AJyuClB;;AgB/uCE;EACE,yBZYc;EYXd,qBZWc;EYVd,WZEW;AJgvCf;;AgBhvCI;EAEE,0BZMY;AJ4uClB;;AgBzvCE;EACE,yBCwCiC;EDvCjC,qBCuCiC;EDtCjC,cZoFmB;AJwqCvB;;AgB1vCI;EAEE,0BCkC+B;AjB0tCrC;;AgBnwCE;EACE,yBCyC2B;EDxC3B,qBCwC2B;EDvC3B,WZEW;AJowCf;;AgBpwCI;EAEE,0BCmCyB;AjBmuC/B;;AgB7wCE;EACE,yBC0C6B;EDzC7B,qBCyC6B;EDxC7B,cZoFmB;AJ4rCvB;;AgB9wCI;EAEE,0BCoC2B;AjB4uCjC;;AgBvxCE;EACE,yBC2C4B;ED1C5B,qBC0C4B;EDzC5B,cZoFmB;AJssCvB;;AgBxxCI;EAEE,0BCqC0B;AjBqvChC;;AgBjyCE;EACE,yBC4C+B;ED3C/B,qBC2C+B;ED1C/B,cZoFmB;AJgtCvB;;AgBlyCI;EAEE,0BCsC6B;AjB8vCnC;;AgB3yCE;EACE,yBC6C8B;ED5C9B,qBC4C8B;ED3C9B,cZoFmB;AJ0tCvB;;AgB5yCI;EAEE,0BCuC4B;AjBuwClC;;AgBrzCE;EACE,yBC4BiB;ED3BjB,qBC2BiB;ED1BjB,WZEW;AJszCf;;AgBtzCI;EAEE,0BCsBe;AjBkyCrB;;AgB/zCE;EACE,yBC6BiB;ED5BjB,qBC4BiB;ED3BjB,WZEW;AJg0Cf;;AgBh0CI;EAEE,0BCuBe;AjB2yCrB;;AgBz0CE;EACE,yBC8BiB;ED7BjB,qBC6BiB;ED5BjB,WZEW;AJ00Cf;;AgB10CI;EAEE,0BCwBe;AjBozCrB;;AgBn1CE;EACE,yBC+BiB;ED9BjB,qBC8BiB;ED7BjB,WZEW;AJo1Cf;;AgBp1CI;EAEE,0BCyBe;AjB6zCrB;;AgB71CE;EACE,yBCgCiB;ED/BjB,qBC+BiB;ED9BjB,WZEW;AJ81Cf;;AgB91CI;EAEE,0BC0Be;AjBs0CrB;;AgBv2CE;EACE,yBCiCiB;EDhCjB,qBCgCiB;ED/BjB,cZoFmB;AJsxCvB;;AgBx2CI;EAEE,0BC2Be;AjB+0CrB;;AgBj3CE;EACE,yBCkCiB;EDjCjB,qBCiCiB;EDhCjB,cZoFmB;AJgyCvB;;AgBl3CI;EAEE,0BC4Be;AjBw1CrB;;AgB33CE;EACE,yBCmCiB;EDlCjB,qBCkCiB;EDjCjB,WZEW;AJ43Cf;;AgB53CI;EAEE,0BC6Be;AjBi2CrB;;AgBr4CE;EACE,yBCoCiB;EDnCjB,qBCmCiB;EDlCjB,WZEW;AJs4Cf;;AgBt4CI;EAEE,0BC8Be;AjB02CrB;;AgB/4CE;EACE,yBCqCiB;EDpCjB,qBCoCiB;EDnCjB,WZEW;AJg5Cf;;AgBh5CI;EAEE,0BC+Be;AjBm3CrB;;AgBz5CE;EACE,sBZIW;EYHX,kBZGW;EYFX,cZoFmB;AJw0CvB;;AgB15CI;EAEE,uBZFS;AJ85Cf;;AgBn6CE;EACE,yBZUc;EYTd,qBZSc;EYRd,WZEW;AJo6Cf;;AgBp6CI;EAEE,0BZIY;AJk6ClB;;AgB76CE;EACE,yBZYc;EYXd,qBZWc;EYVd,WZEW;AJ86Cf;;AgB96CI;EAEE,0BZMY;AJ06ClB;;AkBz7CA;EHAE,eAAe;EACf,gBAAgB;Af67ClB;;AkB97CA;EAII,WAAW;EACX,aAAa;EACb,kBAAkB;EAClB,UAAU;AlB87Cd;;AkBr8CA;EfcI,kBeJ4B;EAC1B,YAAY;EACZ,eAAe;AlB+7CrB;;AkB38CA;;EAkBQ,WAAW;AlB87CnB;;AkBx7CA;;EAEE,cAAc;AlB27ChB;;AkBx7CA;EACE,cdhBgB;EciBhB,mBd4MoD;Ec3MpD,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;AlB27CrB;;AkBx7CA;EACE,cAA6B;EAC7B,eAAe;AlB27CjB;;AkBx7CA;EAEI,cdjCc;AJ29ClB;;AkB57CA;EAKI,cdnCc;AJ89ClB;;AmBz+CA;EACE,SAAS;EACT,kBAAkB;AnB4+CpB;;AmBx+CA;EhBgBI,+BC0LgC;EDzLhC,gCCyLgC;EelMhC,aAAa;EACb,aAAa;EACb,kBAAkB;AnBs+CtB;;AmBh/CA;EAeI,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,aAAa;EACb,yCfTW;AJ8+Cf;;AmBx/CA;EAwBI,aAAa;AnBo+CjB;;AmB5/CA;EA6BI,SAAS;EACT,kBAAkB;EAClB,kBAAkB;EAClB,SAAS;AnBm+Cb;;AmBngDA;EAmCM,sBfnCS;EeoCT,YAAY;EACZ,WAAW;AnBo+CjB;;AmBzgDA;EA0CI,iBAAiB;AnBm+CrB;;AmB99CA;EhB/BI,+BC0LgC;EDzLhC,gCCyLgC;EetJhC,aAAa;AnB+9CjB;;AmBp+CA;EAUI,eAAe;EACf,gBAAgB;EAChB,kBAAkB;EAClB,eAAe;AnB89CnB;;AmB3+CA;EAkBI,aAAa;AnB69CjB;;AmB/+CA;;EAuBI,iBAAiB;AnB69CrB;;AmBp/CA;EA6BM,WAAW;EACX,YAAY;EACZ,WAAW;AnB29CjB", "file": "adminlte.extra-components.css", "sourcesContent": ["/*!\n *   AdminLTE v3.1.0\n *     Only Extra Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n\n// Bootstrap\n// ---------------------------------------------------\n@import \"~bootstrap/scss/functions\";\n@import \"../bootstrap-variables\";\n@import \"~bootstrap/scss/mixins\";\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import \"../variables\";\n@import \"../variables-alt\";\n@import \"../mixins\";\n\n@import \"extra-components\";\n", "/*!\n *   AdminLTE v3.1.0\n *     Only Extra Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes shake {\n  0% {\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes wobble {\n  0% {\n    transform: none;\n  }\n  15% {\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n  30% {\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n  45% {\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n  60% {\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n  75% {\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n  100% {\n    transform: none;\n  }\n}\n\n.small-box {\n  border-radius: 0.25rem;\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  display: block;\n  margin-bottom: 20px;\n  position: relative;\n}\n\n.small-box > .inner {\n  padding: 10px;\n}\n\n.small-box > .small-box-footer {\n  background-color: rgba(0, 0, 0, 0.1);\n  color: rgba(255, 255, 255, 0.8);\n  display: block;\n  padding: 3px 0;\n  position: relative;\n  text-align: center;\n  text-decoration: none;\n  z-index: 10;\n}\n\n.small-box > .small-box-footer:hover {\n  background-color: rgba(0, 0, 0, 0.15);\n  color: #fff;\n}\n\n.small-box h3 {\n  font-size: 2.2rem;\n  font-weight: 700;\n  margin: 0 0 10px;\n  padding: 0;\n  white-space: nowrap;\n}\n\n@media (min-width: 992px) {\n  .col-xl-2 .small-box h3,\n  .col-lg-2 .small-box h3,\n  .col-md-2 .small-box h3 {\n    font-size: 1.6rem;\n  }\n  .col-xl-3 .small-box h3,\n  .col-lg-3 .small-box h3,\n  .col-md-3 .small-box h3 {\n    font-size: 1.6rem;\n  }\n}\n\n@media (min-width: 1200px) {\n  .col-xl-2 .small-box h3,\n  .col-lg-2 .small-box h3,\n  .col-md-2 .small-box h3 {\n    font-size: 2.2rem;\n  }\n  .col-xl-3 .small-box h3,\n  .col-lg-3 .small-box h3,\n  .col-md-3 .small-box h3 {\n    font-size: 2.2rem;\n  }\n}\n\n.small-box p {\n  font-size: 1rem;\n}\n\n.small-box p > small {\n  color: #f8f9fa;\n  display: block;\n  font-size: .9rem;\n  margin-top: 5px;\n}\n\n.small-box h3,\n.small-box p {\n  z-index: 5;\n}\n\n.small-box .icon {\n  color: rgba(0, 0, 0, 0.15);\n  z-index: 0;\n}\n\n.small-box .icon > i {\n  font-size: 90px;\n  position: absolute;\n  right: 15px;\n  top: 15px;\n  transition: transform 0.3s linear;\n}\n\n.small-box .icon > i.fa, .small-box .icon > i.fas, .small-box .icon > i.far, .small-box .icon > i.fab, .small-box .icon > i.fal, .small-box .icon > i.fad, .small-box .icon > i.ion {\n  font-size: 70px;\n  top: 20px;\n}\n\n.small-box .icon svg {\n  font-size: 70px;\n  position: absolute;\n  right: 15px;\n  top: 15px;\n  transition: transform 0.3s linear;\n}\n\n.small-box:hover {\n  text-decoration: none;\n}\n\n.small-box:hover .icon > i, .small-box:hover .icon > i.fa, .small-box:hover .icon > i.fas, .small-box:hover .icon > i.far, .small-box:hover .icon > i.fab, .small-box:hover .icon > i.fal, .small-box:hover .icon > i.fad, .small-box:hover .icon > i.ion {\n  transform: scale(1.1);\n}\n\n.small-box:hover .icon > svg {\n  transform: scale(1.1);\n}\n\n@media (max-width: 767.98px) {\n  .small-box {\n    text-align: center;\n  }\n  .small-box .icon {\n    display: none;\n  }\n  .small-box p {\n    font-size: 12px;\n  }\n}\n\n.info-box {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  border-radius: 0.25rem;\n  background-color: #fff;\n  display: flex;\n  margin-bottom: 1rem;\n  min-height: 80px;\n  padding: .5rem;\n  position: relative;\n  width: 100%;\n}\n\n.info-box .progress {\n  background-color: rgba(0, 0, 0, 0.125);\n  height: 2px;\n  margin: 5px 0;\n}\n\n.info-box .progress .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box-icon {\n  border-radius: 0.25rem;\n  align-items: center;\n  display: flex;\n  font-size: 1.875rem;\n  justify-content: center;\n  text-align: center;\n  width: 70px;\n}\n\n.info-box .info-box-icon > img {\n  max-width: 100%;\n}\n\n.info-box .info-box-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  line-height: 1.8;\n  flex: 1;\n  padding: 0 10px;\n}\n\n.info-box .info-box-number {\n  display: block;\n  margin-top: .25rem;\n  font-weight: 700;\n}\n\n.info-box .progress-description,\n.info-box .info-box-text {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.info-box .info-box .bg-primary,\n.info-box .info-box .bg-gradient-primary {\n  color: #fff;\n}\n\n.info-box .info-box .bg-primary .progress-bar,\n.info-box .info-box .bg-gradient-primary .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-secondary,\n.info-box .info-box .bg-gradient-secondary {\n  color: #fff;\n}\n\n.info-box .info-box .bg-secondary .progress-bar,\n.info-box .info-box .bg-gradient-secondary .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-success,\n.info-box .info-box .bg-gradient-success {\n  color: #fff;\n}\n\n.info-box .info-box .bg-success .progress-bar,\n.info-box .info-box .bg-gradient-success .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-info,\n.info-box .info-box .bg-gradient-info {\n  color: #fff;\n}\n\n.info-box .info-box .bg-info .progress-bar,\n.info-box .info-box .bg-gradient-info .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-warning,\n.info-box .info-box .bg-gradient-warning {\n  color: #1f2d3d;\n}\n\n.info-box .info-box .bg-warning .progress-bar,\n.info-box .info-box .bg-gradient-warning .progress-bar {\n  background-color: #1f2d3d;\n}\n\n.info-box .info-box .bg-danger,\n.info-box .info-box .bg-gradient-danger {\n  color: #fff;\n}\n\n.info-box .info-box .bg-danger .progress-bar,\n.info-box .info-box .bg-gradient-danger .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-light,\n.info-box .info-box .bg-gradient-light {\n  color: #1f2d3d;\n}\n\n.info-box .info-box .bg-light .progress-bar,\n.info-box .info-box .bg-gradient-light .progress-bar {\n  background-color: #1f2d3d;\n}\n\n.info-box .info-box .bg-dark,\n.info-box .info-box .bg-gradient-dark {\n  color: #fff;\n}\n\n.info-box .info-box .bg-dark .progress-bar,\n.info-box .info-box .bg-gradient-dark .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box-more {\n  display: block;\n}\n\n.info-box .progress-description {\n  margin: 0;\n}\n\n@media (min-width: 768px) {\n  .col-xl-2 .info-box .progress-description,\n  .col-lg-2 .info-box .progress-description,\n  .col-md-2 .info-box .progress-description {\n    display: none;\n  }\n  .col-xl-3 .info-box .progress-description,\n  .col-lg-3 .info-box .progress-description,\n  .col-md-3 .info-box .progress-description {\n    display: none;\n  }\n}\n\n@media (min-width: 992px) {\n  .col-xl-2 .info-box .progress-description,\n  .col-lg-2 .info-box .progress-description,\n  .col-md-2 .info-box .progress-description {\n    font-size: 0.75rem;\n    display: block;\n  }\n  .col-xl-3 .info-box .progress-description,\n  .col-lg-3 .info-box .progress-description,\n  .col-md-3 .info-box .progress-description {\n    font-size: 0.75rem;\n    display: block;\n  }\n}\n\n@media (min-width: 1200px) {\n  .col-xl-2 .info-box .progress-description,\n  .col-lg-2 .info-box .progress-description,\n  .col-md-2 .info-box .progress-description {\n    font-size: 1rem;\n    display: block;\n  }\n  .col-xl-3 .info-box .progress-description,\n  .col-lg-3 .info-box .progress-description,\n  .col-md-3 .info-box .progress-description {\n    font-size: 1rem;\n    display: block;\n  }\n}\n\n.dark-mode .info-box {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-primary,\n.dark-mode .info-box .info-box .bg-gradient-primary {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-primary .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-primary .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-secondary,\n.dark-mode .info-box .info-box .bg-gradient-secondary {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-secondary .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-secondary .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-success,\n.dark-mode .info-box .info-box .bg-gradient-success {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-success .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-success .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-info,\n.dark-mode .info-box .info-box .bg-gradient-info {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-info .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-info .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-warning,\n.dark-mode .info-box .info-box .bg-gradient-warning {\n  color: #1f2d3d;\n}\n\n.dark-mode .info-box .info-box .bg-warning .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-warning .progress-bar {\n  background-color: #1f2d3d;\n}\n\n.dark-mode .info-box .info-box .bg-danger,\n.dark-mode .info-box .info-box .bg-gradient-danger {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-danger .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-danger .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-light,\n.dark-mode .info-box .info-box .bg-gradient-light {\n  color: #1f2d3d;\n}\n\n.dark-mode .info-box .info-box .bg-light .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-light .progress-bar {\n  background-color: #1f2d3d;\n}\n\n.dark-mode .info-box .info-box .bg-dark,\n.dark-mode .info-box .info-box .bg-gradient-dark {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-dark .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-dark .progress-bar {\n  background-color: #fff;\n}\n\n.timeline {\n  margin: 0 0 45px;\n  padding: 0;\n  position: relative;\n}\n\n.timeline::before {\n  border-radius: 0.25rem;\n  background-color: #dee2e6;\n  bottom: 0;\n  content: \"\";\n  left: 31px;\n  margin: 0;\n  position: absolute;\n  top: 0;\n  width: 4px;\n}\n\n.timeline > div {\n  margin-bottom: 15px;\n  margin-right: 10px;\n  position: relative;\n}\n\n.timeline > div::before, .timeline > div::after {\n  content: \"\";\n  display: table;\n}\n\n.timeline > div > .timeline-item {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  border-radius: 0.25rem;\n  background-color: #fff;\n  color: #495057;\n  margin-left: 60px;\n  margin-right: 15px;\n  margin-top: 0;\n  padding: 0;\n  position: relative;\n}\n\n.timeline > div > .timeline-item > .time {\n  color: #999;\n  float: right;\n  font-size: 12px;\n  padding: 10px;\n}\n\n.timeline > div > .timeline-item > .timeline-header {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  color: #495057;\n  font-size: 16px;\n  line-height: 1.1;\n  margin: 0;\n  padding: 10px;\n}\n\n.timeline > div > .timeline-item > .timeline-header > a {\n  font-weight: 600;\n}\n\n.timeline > div > .timeline-item > .timeline-body,\n.timeline > div > .timeline-item > .timeline-footer {\n  padding: 10px;\n}\n\n.timeline > div > .timeline-item > .timeline-body > img {\n  margin: 10px;\n}\n\n.timeline > div > .timeline-item > .timeline-body > dl,\n.timeline > div > .timeline-item > .timeline-body ol,\n.timeline > div > .timeline-item > .timeline-body ul {\n  margin: 0;\n}\n\n.timeline > div > .timeline-item > .timeline-footer > a {\n  color: #fff;\n}\n\n.timeline > div > .fa,\n.timeline > div > .fas,\n.timeline > div > .far,\n.timeline > div > .fab,\n.timeline > div > .fal,\n.timeline > div > .fad,\n.timeline > div > .svg-inline--fa,\n.timeline > div > .ion {\n  background-color: #adb5bd;\n  border-radius: 50%;\n  font-size: 16px;\n  height: 30px;\n  left: 18px;\n  line-height: 30px;\n  position: absolute;\n  text-align: center;\n  top: 0;\n  width: 30px;\n}\n\n.timeline > div > .svg-inline--fa {\n  padding: 7px;\n}\n\n.timeline > .time-label > span {\n  border-radius: 4px;\n  background-color: #fff;\n  display: inline-block;\n  font-weight: 600;\n  padding: 5px;\n}\n\n.timeline-inverse > div > .timeline-item {\n  box-shadow: none;\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n}\n\n.timeline-inverse > div > .timeline-item > .timeline-header {\n  border-bottom-color: #dee2e6;\n}\n\n.dark-mode .timeline::before {\n  background-color: #6c757d;\n}\n\n.dark-mode .timeline > div > .timeline-item {\n  background-color: #343a40;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .timeline > div > .timeline-item > .timeline-header {\n  color: #ced4da;\n  border-color: #6c757d;\n}\n\n.dark-mode .timeline > div > .timeline-item > .time {\n  color: #ced4da;\n}\n\n.products-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.products-list > .item {\n  border-radius: 0.25rem;\n  background-color: #fff;\n  padding: 10px 0;\n}\n\n.products-list > .item::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.products-list .product-img {\n  float: left;\n}\n\n.products-list .product-img img {\n  height: 50px;\n  width: 50px;\n}\n\n.products-list .product-info {\n  margin-left: 60px;\n}\n\n.products-list .product-title {\n  font-weight: 600;\n}\n\n.products-list .product-description {\n  color: #6c757d;\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.product-list-in-card > .item {\n  border-radius: 0;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.product-list-in-card > .item:last-of-type {\n  border-bottom-width: 0;\n}\n\n.dark-mode .products-list > .item {\n  background-color: #343a40;\n  color: #fff;\n  border-bottom-color: #6c757d;\n}\n\n.dark-mode .product-description {\n  color: #ced4da;\n}\n\n.direct-chat .card-body {\n  overflow-x: hidden;\n  padding: 0;\n  position: relative;\n}\n\n.direct-chat.chat-pane-open .direct-chat-contacts {\n  transform: translate(0, 0);\n}\n\n.direct-chat.timestamp-light .direct-chat-timestamp {\n  color: #30465f;\n}\n\n.direct-chat.timestamp-dark .direct-chat-timestamp {\n  color: #cccccc;\n}\n\n.direct-chat-messages {\n  transform: translate(0, 0);\n  height: 250px;\n  overflow: auto;\n  padding: 10px;\n}\n\n.direct-chat-msg,\n.direct-chat-text {\n  display: block;\n}\n\n.direct-chat-msg {\n  margin-bottom: 10px;\n}\n\n.direct-chat-msg::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.direct-chat-messages,\n.direct-chat-contacts {\n  transition: transform .5s ease-in-out;\n}\n\n.direct-chat-text {\n  border-radius: 0.3rem;\n  background-color: #d2d6de;\n  border: 1px solid #d2d6de;\n  color: #444;\n  margin: 5px 0 0 50px;\n  padding: 5px 10px;\n  position: relative;\n}\n\n.direct-chat-text::after, .direct-chat-text::before {\n  border: solid transparent;\n  border-right-color: #d2d6de;\n  content: \" \";\n  height: 0;\n  pointer-events: none;\n  position: absolute;\n  right: 100%;\n  top: 15px;\n  width: 0;\n}\n\n.direct-chat-text::after {\n  border-width: 5px;\n  margin-top: -5px;\n}\n\n.direct-chat-text::before {\n  border-width: 6px;\n  margin-top: -6px;\n}\n\n.right .direct-chat-text {\n  margin-left: 0;\n  margin-right: 50px;\n}\n\n.right .direct-chat-text::after, .right .direct-chat-text::before {\n  border-left-color: #d2d6de;\n  border-right-color: transparent;\n  left: 100%;\n  right: auto;\n}\n\n.direct-chat-img {\n  border-radius: 50%;\n  float: left;\n  height: 40px;\n  width: 40px;\n}\n\n.right .direct-chat-img {\n  float: right;\n}\n\n.direct-chat-infos {\n  display: block;\n  font-size: 0.875rem;\n  margin-bottom: 2px;\n}\n\n.direct-chat-name {\n  font-weight: 600;\n}\n\n.direct-chat-timestamp {\n  color: #697582;\n}\n\n.direct-chat-contacts-open .direct-chat-contacts {\n  transform: translate(0, 0);\n}\n\n.direct-chat-contacts {\n  transform: translate(101%, 0);\n  background-color: #343a40;\n  bottom: 0;\n  color: #fff;\n  height: 250px;\n  overflow: auto;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n\n.direct-chat-contacts-light {\n  background-color: #f8f9fa;\n}\n\n.direct-chat-contacts-light .contacts-list-name {\n  color: #495057;\n}\n\n.direct-chat-contacts-light .contacts-list-date {\n  color: #6c757d;\n}\n\n.direct-chat-contacts-light .contacts-list-msg {\n  color: #545b62;\n}\n\n.contacts-list {\n  padding-left: 0;\n  list-style: none;\n}\n\n.contacts-list > li {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.2);\n  margin: 0;\n  padding: 10px;\n}\n\n.contacts-list > li::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.contacts-list > li:last-of-type {\n  border-bottom: 0;\n}\n\n.contacts-list-img {\n  border-radius: 50%;\n  float: left;\n  width: 40px;\n}\n\n.contacts-list-info {\n  color: #fff;\n  margin-left: 45px;\n}\n\n.contacts-list-name,\n.contacts-list-status {\n  display: block;\n}\n\n.contacts-list-name {\n  font-weight: 600;\n}\n\n.contacts-list-status {\n  font-size: 0.875rem;\n}\n\n.contacts-list-date {\n  color: #ced4da;\n  font-weight: 400;\n}\n\n.contacts-list-msg {\n  color: #b1bbc4;\n}\n\n.direct-chat-primary .right > .direct-chat-text {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: #fff;\n}\n\n.direct-chat-primary .right > .direct-chat-text::after, .direct-chat-primary .right > .direct-chat-text::before {\n  border-left-color: #007bff;\n}\n\n.direct-chat-secondary .right > .direct-chat-text {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.direct-chat-secondary .right > .direct-chat-text::after, .direct-chat-secondary .right > .direct-chat-text::before {\n  border-left-color: #6c757d;\n}\n\n.direct-chat-success .right > .direct-chat-text {\n  background-color: #28a745;\n  border-color: #28a745;\n  color: #fff;\n}\n\n.direct-chat-success .right > .direct-chat-text::after, .direct-chat-success .right > .direct-chat-text::before {\n  border-left-color: #28a745;\n}\n\n.direct-chat-info .right > .direct-chat-text {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n  color: #fff;\n}\n\n.direct-chat-info .right > .direct-chat-text::after, .direct-chat-info .right > .direct-chat-text::before {\n  border-left-color: #17a2b8;\n}\n\n.direct-chat-warning .right > .direct-chat-text {\n  background-color: #ffc107;\n  border-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.direct-chat-warning .right > .direct-chat-text::after, .direct-chat-warning .right > .direct-chat-text::before {\n  border-left-color: #ffc107;\n}\n\n.direct-chat-danger .right > .direct-chat-text {\n  background-color: #dc3545;\n  border-color: #dc3545;\n  color: #fff;\n}\n\n.direct-chat-danger .right > .direct-chat-text::after, .direct-chat-danger .right > .direct-chat-text::before {\n  border-left-color: #dc3545;\n}\n\n.direct-chat-light .right > .direct-chat-text {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.direct-chat-light .right > .direct-chat-text::after, .direct-chat-light .right > .direct-chat-text::before {\n  border-left-color: #f8f9fa;\n}\n\n.direct-chat-dark .right > .direct-chat-text {\n  background-color: #343a40;\n  border-color: #343a40;\n  color: #fff;\n}\n\n.direct-chat-dark .right > .direct-chat-text::after, .direct-chat-dark .right > .direct-chat-text::before {\n  border-left-color: #343a40;\n}\n\n.direct-chat-lightblue .right > .direct-chat-text {\n  background-color: #3c8dbc;\n  border-color: #3c8dbc;\n  color: #fff;\n}\n\n.direct-chat-lightblue .right > .direct-chat-text::after, .direct-chat-lightblue .right > .direct-chat-text::before {\n  border-left-color: #3c8dbc;\n}\n\n.direct-chat-navy .right > .direct-chat-text {\n  background-color: #001f3f;\n  border-color: #001f3f;\n  color: #fff;\n}\n\n.direct-chat-navy .right > .direct-chat-text::after, .direct-chat-navy .right > .direct-chat-text::before {\n  border-left-color: #001f3f;\n}\n\n.direct-chat-olive .right > .direct-chat-text {\n  background-color: #3d9970;\n  border-color: #3d9970;\n  color: #fff;\n}\n\n.direct-chat-olive .right > .direct-chat-text::after, .direct-chat-olive .right > .direct-chat-text::before {\n  border-left-color: #3d9970;\n}\n\n.direct-chat-lime .right > .direct-chat-text {\n  background-color: #01ff70;\n  border-color: #01ff70;\n  color: #1f2d3d;\n}\n\n.direct-chat-lime .right > .direct-chat-text::after, .direct-chat-lime .right > .direct-chat-text::before {\n  border-left-color: #01ff70;\n}\n\n.direct-chat-fuchsia .right > .direct-chat-text {\n  background-color: #f012be;\n  border-color: #f012be;\n  color: #fff;\n}\n\n.direct-chat-fuchsia .right > .direct-chat-text::after, .direct-chat-fuchsia .right > .direct-chat-text::before {\n  border-left-color: #f012be;\n}\n\n.direct-chat-maroon .right > .direct-chat-text {\n  background-color: #d81b60;\n  border-color: #d81b60;\n  color: #fff;\n}\n\n.direct-chat-maroon .right > .direct-chat-text::after, .direct-chat-maroon .right > .direct-chat-text::before {\n  border-left-color: #d81b60;\n}\n\n.direct-chat-blue .right > .direct-chat-text {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: #fff;\n}\n\n.direct-chat-blue .right > .direct-chat-text::after, .direct-chat-blue .right > .direct-chat-text::before {\n  border-left-color: #007bff;\n}\n\n.direct-chat-indigo .right > .direct-chat-text {\n  background-color: #6610f2;\n  border-color: #6610f2;\n  color: #fff;\n}\n\n.direct-chat-indigo .right > .direct-chat-text::after, .direct-chat-indigo .right > .direct-chat-text::before {\n  border-left-color: #6610f2;\n}\n\n.direct-chat-purple .right > .direct-chat-text {\n  background-color: #6f42c1;\n  border-color: #6f42c1;\n  color: #fff;\n}\n\n.direct-chat-purple .right > .direct-chat-text::after, .direct-chat-purple .right > .direct-chat-text::before {\n  border-left-color: #6f42c1;\n}\n\n.direct-chat-pink .right > .direct-chat-text {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n  color: #fff;\n}\n\n.direct-chat-pink .right > .direct-chat-text::after, .direct-chat-pink .right > .direct-chat-text::before {\n  border-left-color: #e83e8c;\n}\n\n.direct-chat-red .right > .direct-chat-text {\n  background-color: #dc3545;\n  border-color: #dc3545;\n  color: #fff;\n}\n\n.direct-chat-red .right > .direct-chat-text::after, .direct-chat-red .right > .direct-chat-text::before {\n  border-left-color: #dc3545;\n}\n\n.direct-chat-orange .right > .direct-chat-text {\n  background-color: #fd7e14;\n  border-color: #fd7e14;\n  color: #1f2d3d;\n}\n\n.direct-chat-orange .right > .direct-chat-text::after, .direct-chat-orange .right > .direct-chat-text::before {\n  border-left-color: #fd7e14;\n}\n\n.direct-chat-yellow .right > .direct-chat-text {\n  background-color: #ffc107;\n  border-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.direct-chat-yellow .right > .direct-chat-text::after, .direct-chat-yellow .right > .direct-chat-text::before {\n  border-left-color: #ffc107;\n}\n\n.direct-chat-green .right > .direct-chat-text {\n  background-color: #28a745;\n  border-color: #28a745;\n  color: #fff;\n}\n\n.direct-chat-green .right > .direct-chat-text::after, .direct-chat-green .right > .direct-chat-text::before {\n  border-left-color: #28a745;\n}\n\n.direct-chat-teal .right > .direct-chat-text {\n  background-color: #20c997;\n  border-color: #20c997;\n  color: #fff;\n}\n\n.direct-chat-teal .right > .direct-chat-text::after, .direct-chat-teal .right > .direct-chat-text::before {\n  border-left-color: #20c997;\n}\n\n.direct-chat-cyan .right > .direct-chat-text {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n  color: #fff;\n}\n\n.direct-chat-cyan .right > .direct-chat-text::after, .direct-chat-cyan .right > .direct-chat-text::before {\n  border-left-color: #17a2b8;\n}\n\n.direct-chat-white .right > .direct-chat-text {\n  background-color: #fff;\n  border-color: #fff;\n  color: #1f2d3d;\n}\n\n.direct-chat-white .right > .direct-chat-text::after, .direct-chat-white .right > .direct-chat-text::before {\n  border-left-color: #fff;\n}\n\n.direct-chat-gray .right > .direct-chat-text {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.direct-chat-gray .right > .direct-chat-text::after, .direct-chat-gray .right > .direct-chat-text::before {\n  border-left-color: #6c757d;\n}\n\n.direct-chat-gray-dark .right > .direct-chat-text {\n  background-color: #343a40;\n  border-color: #343a40;\n  color: #fff;\n}\n\n.direct-chat-gray-dark .right > .direct-chat-text::after, .direct-chat-gray-dark .right > .direct-chat-text::before {\n  border-left-color: #343a40;\n}\n\n.dark-mode .direct-chat-text {\n  background-color: #454d55;\n  border-color: #4b545c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-text::after, .dark-mode .direct-chat-text::before {\n  border-right-color: #4b545c;\n}\n\n.dark-mode .direct-chat-timestamp {\n  color: #adb5bd;\n}\n\n.dark-mode .right > .direct-chat-text::after, .dark-mode .right > .direct-chat-text::before {\n  border-right-color: transparent;\n}\n\n.dark-mode .direct-chat-primary .right > .direct-chat-text {\n  background-color: #3f6791;\n  border-color: #3f6791;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-primary .right > .direct-chat-text::after, .dark-mode .direct-chat-primary .right > .direct-chat-text::before {\n  border-left-color: #3f6791;\n}\n\n.dark-mode .direct-chat-secondary .right > .direct-chat-text {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-secondary .right > .direct-chat-text::after, .dark-mode .direct-chat-secondary .right > .direct-chat-text::before {\n  border-left-color: #6c757d;\n}\n\n.dark-mode .direct-chat-success .right > .direct-chat-text {\n  background-color: #00bc8c;\n  border-color: #00bc8c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-success .right > .direct-chat-text::after, .dark-mode .direct-chat-success .right > .direct-chat-text::before {\n  border-left-color: #00bc8c;\n}\n\n.dark-mode .direct-chat-info .right > .direct-chat-text {\n  background-color: #3498db;\n  border-color: #3498db;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-info .right > .direct-chat-text::after, .dark-mode .direct-chat-info .right > .direct-chat-text::before {\n  border-left-color: #3498db;\n}\n\n.dark-mode .direct-chat-warning .right > .direct-chat-text {\n  background-color: #f39c12;\n  border-color: #f39c12;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-warning .right > .direct-chat-text::after, .dark-mode .direct-chat-warning .right > .direct-chat-text::before {\n  border-left-color: #f39c12;\n}\n\n.dark-mode .direct-chat-danger .right > .direct-chat-text {\n  background-color: #e74c3c;\n  border-color: #e74c3c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-danger .right > .direct-chat-text::after, .dark-mode .direct-chat-danger .right > .direct-chat-text::before {\n  border-left-color: #e74c3c;\n}\n\n.dark-mode .direct-chat-light .right > .direct-chat-text {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-light .right > .direct-chat-text::after, .dark-mode .direct-chat-light .right > .direct-chat-text::before {\n  border-left-color: #f8f9fa;\n}\n\n.dark-mode .direct-chat-dark .right > .direct-chat-text {\n  background-color: #343a40;\n  border-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-dark .right > .direct-chat-text::after, .dark-mode .direct-chat-dark .right > .direct-chat-text::before {\n  border-left-color: #343a40;\n}\n\n.dark-mode .direct-chat-lightblue .right > .direct-chat-text {\n  background-color: #86bad8;\n  border-color: #86bad8;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-lightblue .right > .direct-chat-text::after, .dark-mode .direct-chat-lightblue .right > .direct-chat-text::before {\n  border-left-color: #86bad8;\n}\n\n.dark-mode .direct-chat-navy .right > .direct-chat-text {\n  background-color: #002c59;\n  border-color: #002c59;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-navy .right > .direct-chat-text::after, .dark-mode .direct-chat-navy .right > .direct-chat-text::before {\n  border-left-color: #002c59;\n}\n\n.dark-mode .direct-chat-olive .right > .direct-chat-text {\n  background-color: #74c8a3;\n  border-color: #74c8a3;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-olive .right > .direct-chat-text::after, .dark-mode .direct-chat-olive .right > .direct-chat-text::before {\n  border-left-color: #74c8a3;\n}\n\n.dark-mode .direct-chat-lime .right > .direct-chat-text {\n  background-color: #67ffa9;\n  border-color: #67ffa9;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-lime .right > .direct-chat-text::after, .dark-mode .direct-chat-lime .right > .direct-chat-text::before {\n  border-left-color: #67ffa9;\n}\n\n.dark-mode .direct-chat-fuchsia .right > .direct-chat-text {\n  background-color: #f672d8;\n  border-color: #f672d8;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-fuchsia .right > .direct-chat-text::after, .dark-mode .direct-chat-fuchsia .right > .direct-chat-text::before {\n  border-left-color: #f672d8;\n}\n\n.dark-mode .direct-chat-maroon .right > .direct-chat-text {\n  background-color: #ed6c9b;\n  border-color: #ed6c9b;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-maroon .right > .direct-chat-text::after, .dark-mode .direct-chat-maroon .right > .direct-chat-text::before {\n  border-left-color: #ed6c9b;\n}\n\n.dark-mode .direct-chat-blue .right > .direct-chat-text {\n  background-color: #3f6791;\n  border-color: #3f6791;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-blue .right > .direct-chat-text::after, .dark-mode .direct-chat-blue .right > .direct-chat-text::before {\n  border-left-color: #3f6791;\n}\n\n.dark-mode .direct-chat-indigo .right > .direct-chat-text {\n  background-color: #6610f2;\n  border-color: #6610f2;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-indigo .right > .direct-chat-text::after, .dark-mode .direct-chat-indigo .right > .direct-chat-text::before {\n  border-left-color: #6610f2;\n}\n\n.dark-mode .direct-chat-purple .right > .direct-chat-text {\n  background-color: #6f42c1;\n  border-color: #6f42c1;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-purple .right > .direct-chat-text::after, .dark-mode .direct-chat-purple .right > .direct-chat-text::before {\n  border-left-color: #6f42c1;\n}\n\n.dark-mode .direct-chat-pink .right > .direct-chat-text {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-pink .right > .direct-chat-text::after, .dark-mode .direct-chat-pink .right > .direct-chat-text::before {\n  border-left-color: #e83e8c;\n}\n\n.dark-mode .direct-chat-red .right > .direct-chat-text {\n  background-color: #e74c3c;\n  border-color: #e74c3c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-red .right > .direct-chat-text::after, .dark-mode .direct-chat-red .right > .direct-chat-text::before {\n  border-left-color: #e74c3c;\n}\n\n.dark-mode .direct-chat-orange .right > .direct-chat-text {\n  background-color: #fd7e14;\n  border-color: #fd7e14;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-orange .right > .direct-chat-text::after, .dark-mode .direct-chat-orange .right > .direct-chat-text::before {\n  border-left-color: #fd7e14;\n}\n\n.dark-mode .direct-chat-yellow .right > .direct-chat-text {\n  background-color: #f39c12;\n  border-color: #f39c12;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-yellow .right > .direct-chat-text::after, .dark-mode .direct-chat-yellow .right > .direct-chat-text::before {\n  border-left-color: #f39c12;\n}\n\n.dark-mode .direct-chat-green .right > .direct-chat-text {\n  background-color: #00bc8c;\n  border-color: #00bc8c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-green .right > .direct-chat-text::after, .dark-mode .direct-chat-green .right > .direct-chat-text::before {\n  border-left-color: #00bc8c;\n}\n\n.dark-mode .direct-chat-teal .right > .direct-chat-text {\n  background-color: #20c997;\n  border-color: #20c997;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-teal .right > .direct-chat-text::after, .dark-mode .direct-chat-teal .right > .direct-chat-text::before {\n  border-left-color: #20c997;\n}\n\n.dark-mode .direct-chat-cyan .right > .direct-chat-text {\n  background-color: #3498db;\n  border-color: #3498db;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-cyan .right > .direct-chat-text::after, .dark-mode .direct-chat-cyan .right > .direct-chat-text::before {\n  border-left-color: #3498db;\n}\n\n.dark-mode .direct-chat-white .right > .direct-chat-text {\n  background-color: #fff;\n  border-color: #fff;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-white .right > .direct-chat-text::after, .dark-mode .direct-chat-white .right > .direct-chat-text::before {\n  border-left-color: #fff;\n}\n\n.dark-mode .direct-chat-gray .right > .direct-chat-text {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-gray .right > .direct-chat-text::after, .dark-mode .direct-chat-gray .right > .direct-chat-text::before {\n  border-left-color: #6c757d;\n}\n\n.dark-mode .direct-chat-gray-dark .right > .direct-chat-text {\n  background-color: #343a40;\n  border-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-gray-dark .right > .direct-chat-text::after, .dark-mode .direct-chat-gray-dark .right > .direct-chat-text::before {\n  border-left-color: #343a40;\n}\n\n.users-list {\n  padding-left: 0;\n  list-style: none;\n}\n\n.users-list > li {\n  float: left;\n  padding: 10px;\n  text-align: center;\n  width: 25%;\n}\n\n.users-list > li img {\n  border-radius: 50%;\n  height: auto;\n  max-width: 100%;\n}\n\n.users-list > li > a:hover,\n.users-list > li > a:hover .users-list-name {\n  color: #999;\n}\n\n.users-list-name,\n.users-list-date {\n  display: block;\n}\n\n.users-list-name {\n  color: #495057;\n  font-size: 0.875rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.users-list-date {\n  color: #748290;\n  font-size: 12px;\n}\n\n.dark-mode .users-list-name {\n  color: #ced4da;\n}\n\n.dark-mode .users-list-date {\n  color: #adb5bd;\n}\n\n.card-widget {\n  border: 0;\n  position: relative;\n}\n\n.widget-user .widget-user-header {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n  height: 135px;\n  padding: 1rem;\n  text-align: center;\n}\n\n.widget-user .widget-user-username {\n  font-size: 25px;\n  font-weight: 300;\n  margin-bottom: 0;\n  margin-top: 0;\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);\n}\n\n.widget-user .widget-user-desc {\n  margin-top: 0;\n}\n\n.widget-user .widget-user-image {\n  left: 50%;\n  margin-left: -45px;\n  position: absolute;\n  top: 80px;\n}\n\n.widget-user .widget-user-image > img {\n  border: 3px solid #fff;\n  height: auto;\n  width: 90px;\n}\n\n.widget-user .card-footer {\n  padding-top: 50px;\n}\n\n.widget-user-2 .widget-user-header {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n  padding: 1rem;\n}\n\n.widget-user-2 .widget-user-username {\n  font-size: 25px;\n  font-weight: 300;\n  margin-bottom: 5px;\n  margin-top: 5px;\n}\n\n.widget-user-2 .widget-user-desc {\n  margin-top: 0;\n}\n\n.widget-user-2 .widget-user-username,\n.widget-user-2 .widget-user-desc {\n  margin-left: 75px;\n}\n\n.widget-user-2 .widget-user-image > img {\n  float: left;\n  height: auto;\n  width: 65px;\n}\n\n/*# sourceMappingURL=adminlte.extra-components.css.map */", "//\n// Mixins: Animation\n//\n\n\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes shake {\n  0% {\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes wobble {\n  0% {\n    transform: none;\n  }\n\n  15% {\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n\n  30% {\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n\n  45% {\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n\n  60% {\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n\n  75% {\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n\n  100% {\n    transform: none;\n  }\n}\n\n//\n", "//\n// Component: Small Box\n//\n\n.small-box {\n  @include border-radius($border-radius);\n  @include box-shadow($card-shadow);\n\n  display: block;\n  margin-bottom: 20px;\n  position: relative;\n\n  // content wrapper\n  > .inner {\n    padding: 10px;\n  }\n\n  > .small-box-footer {\n    background-color: rgba($black, .1);\n    color: rgba($white, .8);\n    display: block;\n    padding: 3px 0;\n    position: relative;\n    text-align: center;\n    text-decoration: none;\n    z-index: 10;\n\n    &:hover {\n      background-color: rgba($black, .15);\n      color: $white;\n    }\n  }\n\n  h3 {\n    @include font-size(2.2rem);\n    font-weight: 700;\n    margin: 0 0 10px;\n    padding: 0;\n    white-space: nowrap;\n  }\n\n  @include media-breakpoint-up(lg) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      h3 {\n        @include font-size(1.6rem);\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      h3 {\n        @include font-size(1.6rem);\n      }\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      h3 {\n        @include font-size(2.2rem);\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      h3 {\n        @include font-size(2.2rem);\n      }\n    }\n  }\n\n  p {\n    font-size: 1rem;\n\n    > small {\n      color: $gray-100;\n      display: block;\n      font-size: .9rem;\n      margin-top: 5px;\n    }\n  }\n\n  h3,\n  p {\n    z-index: 5;\n  }\n\n  // the icon\n  .icon {\n    color: rgba($black, .15);\n    z-index: 0;\n\n    > i {\n      font-size: 90px;\n      position: absolute;\n      right: 15px;\n      top: 15px;\n      transition: transform $transition-speed linear;\n\n      &.fa,\n      &.fas,\n      &.far,\n      &.fab,\n      &.fal,\n      &.fad,\n      &.ion {\n        font-size: 70px;\n        top: 20px;\n      }\n    }\n\n    svg {\n      font-size: 70px;\n      position: absolute;\n      right: 15px;\n      top: 15px;\n      transition: transform $transition-speed linear;\n    }\n  }\n\n  // Small box hover state\n  &:hover {\n    text-decoration: none;\n\n    // Animate icons on small box hover\n    .icon {\n      > i {\n        &,\n        &.fa,\n        &.fas,\n        &.far,\n        &.fab,\n        &.fal,\n        &.fad,\n        &.ion {\n          transform: scale(1.1);\n        }\n      }\n      > svg {\n        transform: scale(1.1);\n      }\n    }\n  }\n}\n\n@include media-breakpoint-down(sm) {\n  // No need for icons on very small devices\n  .small-box {\n    text-align: center;\n\n    .icon {\n      display: none;\n    }\n\n    p {\n      font-size: 12px;\n    }\n  }\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n//\n// Color system\n//\n\n// stylelint-disable\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge((\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n), $grays);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge((\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n), $colors);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge((\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n), $theme-colors);\n// stylelint-enable\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: #1f2d3d !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              true !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// stylelint-disable\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge((\n  0: 0,\n  1: ($spacer * .25),\n  2: ($spacer * .5),\n  3: $spacer,\n  4: ($spacer * 1.5),\n  5: ($spacer * 3)\n), $spacers);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge((\n  25: 25%,\n  50: 50%,\n  75: 75%,\n  100: 100%\n), $sizes);\n// stylelint-enable\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                theme-color(\"primary\") !default;\n$link-decoration:           none !default;\n$link-hover-color:          darken($link-color, 15%) !default;\n$link-hover-decoration:     none !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           15px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                ($font-size-base * 1.25) !default;\n$font-size-sm:                ($font-size-base * .875) !default;\n\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      ($spacer / 2) !default;\n$headings-font-family:        inherit !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              ($font-size-base * 1.25) !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-bg:                    transparent !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $gray-300 !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-bg:               $gray-900 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($gray-900, 10%) !default;\n$table-dark-color:            $body-bg !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              none !default;\n$btn-focus-width:             0 !default;\n$btn-focus-box-shadow:        none !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       none !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 0 0 rgba($black, 0) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     0 !default;\n$input-focus-box-shadow:                none !default;\n\n$input-placeholder-color:               lighten($gray-600, 15%) !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y / 2}) !default;\n\n$input-height:                          calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:                 ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:                       calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:                 ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:                       calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $gray-300 !default;\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-control-indicator-disabled-bg:          $gray-200 !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n\n$custom-control-indicator-focus-box-shadow:     0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:    $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:  str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:           .375rem !default;\n$custom-select-padding-x:          .75rem !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $white !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-select-border-width:        $input-btn-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-box-shadow:    none !default;\n\n$custom-select-font-size-sm:        75% !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-font-size-lg:        125% !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $custom-select-focus-box-shadow !default;\n\n$custom-file-padding-y:             $input-btn-padding-y !default;\n$custom-file-padding-x:             $input-btn-padding-x !default;\n$custom-file-line-height:           $input-btn-line-height !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-btn-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $custom-select-focus-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n\n// Form validation\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer / 2) !default;\n$navbar-padding-x:                  ($spacer / 2) !default;\n\n$navbar-nav-link-padding-x:         1rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .75) !default;\n$navbar-dark-hover-color:           rgba($white, 1) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 0 !default; //$border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 ($grid-gutter-width / 2) !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:           $font-size-sm !default;\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-border-radius:        $border-radius !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           .25rem !default;\n$tooltip-padding-x:           .5rem !default;\n$tooltip-margin:              0 !default;\n\n$tooltip-arrow-width:         .8rem !default;\n$tooltip-arrow-height:        .4rem !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         1rem !default;\n\n$modal-dialog-margin:         .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black, .2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-border-radius:    $border-radius-lg !default;\n$modal-content-box-shadow-xs:    0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        1rem !default;\n\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                ($font-size-base * .75) !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// List group\n\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:            $white !default;\n$carousel-control-width:            15% !default;\n$carousel-control-opacity:          .5 !default;\n\n$carousel-indicator-width:          30px !default;\n$carousel-indicator-height:         3px !default;\n$carousel-indicator-spacer:         3px !default;\n$carousel-indicator-active-bg:      $white !default;\n\n$carousel-caption-width:            70% !default;\n$carousel-caption-color:            $white !default;\n\n$carousel-control-icon-width:       20px !default;\n\n$carousel-control-prev-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:               transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Printing\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Core: Variables\n//\n\n// COLORS\n// --------------------------------------------------------\n$blue: #0073b7 !default;\n$lightblue: #3c8dbc !default;\n$navy: #001f3f !default;\n$teal: #39cccc !default;\n$olive: #3d9970 !default;\n$lime: #01ff70 !default;\n$orange: #ff851b !default;\n$fuchsia: #f012be !default;\n$purple: #605ca8 !default;\n$maroon: #d81b60 !default;\n$black: #111 !default;\n$gray-x-light: #d2d6de !default;\n\n$colors: map-merge(\n  (\n    \"lightblue\": $lightblue,\n    \"navy\": $navy,\n    \"olive\": $olive,\n    \"lime\": $lime,\n    \"fuchsia\": $fuchsia,\n    \"maroon\": $maroon,\n  ),\n  $colors\n);\n\n// LAYOUT\n// --------------------------------------------------------\n\n$font-size-root: 1rem !default;\n\n// Sidebar\n$sidebar-width: 250px !default;\n$sidebar-padding-x: .5rem !default;\n$sidebar-padding-y: 0 !default;\n$sidebar-custom-height: 4rem !default;\n$sidebar-custom-height-lg: 6rem !default;\n$sidebar-custom-height-xl: 8rem !default;\n$sidebar-custom-padding-x: .85rem !default;\n$sidebar-custom-padding-y: .5rem !default;\n\n// Boxed layout maximum width\n$boxed-layout-max-width: 1250px !default;\n\n// Body background (Affects main content background only)\n$main-bg: #f4f6f9 !default;\n\n$dark-main-bg: lighten($dark, 7.5%) !important;\n\n// Content padding\n$content-padding-y: 0 !default;\n$content-padding-x: $navbar-padding-x !default;\n\n// IMAGE SIZES\n// --------------------------------------------------------\n$img-size-sm: 1.875rem !default;\n$img-size-md: 3.75rem !default;\n$img-size-lg: 6.25rem !default;\n$img-size-push: .625rem !default;\n\n// MAIN HEADER\n// --------------------------------------------------------\n$main-header-bottom-border-width: $border-width !default;\n$main-header-bottom-border-color: $gray-300 !default;\n$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;\n$main-header-link-padding-y: $navbar-padding-y !default;\n$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;\n$nav-link-sm-padding-y: .35rem !default;\n$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;\n$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;\n\n\n// Main header skins\n$main-header-dark-form-control-bg: $gray-800 !default;\n$main-header-dark-form-control-focused-bg: $gray-700 !default;\n$main-header-dark-form-control-focused-color: $gray-400 !default;\n$main-header-dark-form-control-border-color: $gray-600 !default;\n$main-header-dark-form-control-focused-border-color: $gray-600 !default;\n$main-header-dark-placeholder-color: rgba($white, .6) !default;\n\n$main-header-light-form-control-bg: darken($gray-200, 5%) !default;\n$main-header-light-form-control-focused-bg: darken($gray-200, 7.5%) !default;\n$main-header-light-form-control-focused-color: $gray-400 !default;\n$main-header-light-form-control-border-color: $gray-400 !default;\n$main-header-light-form-control-focused-border-color: darken($gray-400, 2.5%) !default;\n$main-header-light-placeholder-color: rgba(0, 0, 0, .6) !default;\n\n// MAIN FOOTER\n// --------------------------------------------------------\n$main-footer-padding: 1rem !default;\n$main-footer-padding-sm: $main-footer-padding * .812 !default;\n$main-footer-border-top-width: 1px !default;\n$main-footer-border-top-color: $gray-300 !default;\n$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;\n$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;\n$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;\n$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-bg: $white !default;\n\n// SIDEBAR SKINS\n// --------------------------------------------------------\n\n// Dark sidebar\n$sidebar-dark-bg: $dark !default;\n$sidebar-dark-hover-bg: rgba(255, 255, 255, .1) !default;\n$sidebar-dark-color: #c2c7d0 !default;\n$sidebar-dark-hover-color: $white !default;\n$sidebar-dark-active-color: $white !default;\n$sidebar-dark-submenu-bg: transparent !default;\n$sidebar-dark-submenu-color: #c2c7d0 !default;\n$sidebar-dark-submenu-hover-color: $white !default;\n$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;\n$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;\n$sidebar-dark-submenu-active-bg: rgba(255, 255, 255, .9) !default;\n\n// Light sidebar\n$sidebar-light-bg: $white !default;\n$sidebar-light-hover-bg: rgba($black, .1) !default;\n$sidebar-light-color: $gray-800 !default;\n$sidebar-light-hover-color: $gray-900 !default;\n$sidebar-light-active-color: $black !default;\n$sidebar-light-submenu-bg: transparent !default;\n$sidebar-light-submenu-color: #777 !default;\n$sidebar-light-submenu-hover-color: $black !default;\n$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;\n$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;\n$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;\n\n// SIDEBAR MINI\n// --------------------------------------------------------\n$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;\n$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;\n$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x / 2) !default;\n\n// CONTROL SIDEBAR\n// --------------------------------------------------------\n$control-sidebar-width: $sidebar-width !default;\n\n// Cards\n// --------------------------------------------------------\n$card-border-color: $gray-100 !default;\n$card-dark-border-color: lighten($gray-900, 10%) !default;\n$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;\n$card-title-font-size: 1.1rem !default;\n$card-title-font-size-sm: 1rem !default;\n$card-title-font-weight: $font-weight-normal !default;\n$card-nav-link-padding-sm-y: .4rem !default;\n$card-nav-link-padding-sm-x: .8rem !default;\n$card-img-size: $img-size-sm !default;\n\n// PROGRESS BARS\n// --------------------------------------------------------\n$progress-bar-border-radius: 1px !default;\n\n// DIRECT CHAT\n// --------------------------------------------------------\n$direct-chat-default-msg-bg: $gray-x-light !default;\n$direct-chat-default-font-color: #444 !default;\n$direct-chat-default-msg-border-color: $gray-x-light !default;\n\n// Z-INDEX\n// --------------------------------------------------------\n$zindex-main-header: $zindex-fixed + 4 !default;\n$zindex-main-sidebar: $zindex-fixed + 8 !default;\n$zindex-main-footer: $zindex-fixed + 2 !default;\n$zindex-control-sidebar: $zindex-fixed + 1 !default;\n$zindex-toasts: $zindex-main-sidebar + 2 !default;\n$zindex-preloader: 9999 !default;\n\n// TRANSITIONS SETTINGS\n// --------------------------------------------------------\n\n// Transition global options\n$transition-speed: .3s !default;\n$transition-fn: ease-in-out !default;\n\n// TEXT\n// --------------------------------------------------------\n$font-size-xs: ($font-size-base * .75) !default;\n$font-size-xl: ($font-size-base * 2) !default;\n\n\n// BUTTON\n// --------------------------------------------------------\n$button-default-background-color: $gray-100 !default;\n$button-default-color: #444 !default;\n$button-default-border-color: #ddd !default;\n\n$button-padding-y-xs: .125rem !default;\n$button-padding-x-xs: .25rem !default;\n$button-line-height-xs: $line-height-sm !default;\n$button-font-size-xs: ($font-size-base * .75) !default;\n$button-border-radius-xs: .15rem !default;\n\n\n// ELEVATION\n// --------------------------------------------------------\n$elevations: ();\n$elevations: map-merge(\n  (\n    1: unquote(\"0 1px 3px \" + rgba($black, .12) + \", 0 1px 2px \" + rgba($black, .24)),\n    2: unquote(\"0 3px 6px \" + rgba($black, .16) + \", 0 3px 6px \" + rgba($black, .23)),\n    3: unquote(\"0 10px 20px \" + rgba($black, .19) + \", 0 6px 6px \" + rgba($black, .23)),\n    4: unquote(\"0 14px 28px \" + rgba($black, .25) + \", 0 10px 10px \" + rgba($black, .22)),\n    5: unquote(\"0 19px 38px \" + rgba($black, .3) + \", 0 15px 12px \" + rgba($black, .22)),\n  ),\n  $elevations\n);\n\n// RIBBON\n// --------------------------------------------------------\n$ribbon-border-size: 3px !default;\n$ribbon-line-height: 100% !default;\n$ribbon-padding: .375rem 0 !default;\n$ribbon-font-size: .8rem !default;\n$ribbon-width: 90px !default;\n$ribbon-wrapper-size: 70px !default;\n$ribbon-top: 10px !default;\n$ribbon-right: -2px !default;\n$ribbon-lg-wrapper-size: 120px !default;\n$ribbon-lg-width: 160px !default;\n$ribbon-lg-top: 26px !default;\n$ribbon-lg-right: 0 !default;\n$ribbon-xl-wrapper-size: 180px !default;\n$ribbon-xl-width: 240px !default;\n$ribbon-xl-top: 47px !default;\n$ribbon-xl-right: 4px !default;\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\n// Component: Info Box\n//\n\n.info-box {\n  @include box-shadow($card-shadow);\n  @include border-radius($border-radius);\n\n  background-color: $white;\n  display: flex;\n  margin-bottom: map-get($spacers, 3);\n  min-height: 80px;\n  padding: .5rem;\n  position: relative;\n  width: 100%;\n\n  .progress {\n    background-color: rgba($black, .125);\n    height: 2px;\n    margin: 5px 0;\n\n    .progress-bar {\n      background-color: $white;\n    }\n  }\n\n  .info-box-icon {\n    @if $enable-rounded {\n      border-radius: $border-radius;\n    }\n\n    align-items: center;\n    display: flex;\n    font-size: 1.875rem;\n    justify-content: center;\n    text-align: center;\n    width: 70px;\n\n    > img {\n      max-width: 100%;\n    }\n  }\n\n  .info-box-content {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    line-height: 1.8;\n    flex: 1;\n    padding: 0 10px;\n  }\n\n  .info-box-number {\n    display: block;\n    margin-top: .25rem;\n    font-weight: $font-weight-bold;\n  }\n\n  .progress-description,\n  .info-box-text {\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  @each $name, $color in $theme-colors {\n    .info-box {\n      .bg-#{$name},\n      .bg-gradient-#{$name} {\n        color: color-yiq($color);\n\n        .progress-bar {\n          background-color: color-yiq($color);\n        }\n      }\n    }\n  }\n\n  .info-box-more {\n    display: block;\n  }\n\n  .progress-description {\n    margin: 0;\n\n  }\n\n  @include media-breakpoint-up(md) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      .progress-description {\n        display: none;\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      .progress-description {\n        display: none;\n      }\n    }\n  }\n\n  @include media-breakpoint-up(lg) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      .progress-description {\n        @include font-size(.75rem);\n        display: block;\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      .progress-description {\n        @include font-size(.75rem);\n        display: block;\n      }\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      .progress-description {\n        @include font-size(1rem);\n        display: block;\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      .progress-description {\n        @include font-size(1rem);\n        display: block;\n      }\n    }\n  }\n}\n\n.dark-mode {\n  .info-box {\n    background-color: $dark;\n    color: $white;\n    @each $name, $color in $theme-colors-alt {\n      .info-box {\n        .bg-#{$name},\n        .bg-gradient-#{$name} {\n          color: color-yiq($color);\n\n          .progress-bar {\n            background-color: color-yiq($color);\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Component: Timeline\n//\n\n.timeline {\n  margin: 0 0 45px;\n  padding: 0;\n  position: relative;\n  // The line\n  &::before {\n    @include border-radius($border-radius);\n    background-color: $gray-300;\n    bottom: 0;\n    content: \"\";\n    left: 31px;\n    margin: 0;\n    position: absolute;\n    top: 0;\n    width: 4px;\n  }\n  // Element\n  > div {\n    &::before,\n    &::after {\n      content: \"\";\n      display: table;\n    }\n\n    margin-bottom: 15px;\n    margin-right: 10px;\n    position: relative;\n    // The content\n    > .timeline-item {\n      @include box-shadow($card-shadow);\n      @include border-radius($border-radius);\n      background-color: $white;\n      color: $gray-700;\n      margin-left: 60px;\n      margin-right: 15px;\n      margin-top: 0;\n      padding: 0;\n      position: relative;\n      // The time and header\n      > .time {\n        color: #999;\n        float: right;\n        font-size: 12px;\n        padding: 10px;\n      }\n      // Header\n      > .timeline-header {\n        border-bottom: 1px solid $card-border-color;\n        color: $gray-700;\n        font-size: 16px;\n        line-height: 1.1;\n        margin: 0;\n        padding: 10px;\n        // Link in header\n        > a {\n          font-weight: 600;\n        }\n      }\n      // Item body and footer\n      > .timeline-body,\n      > .timeline-footer {\n        padding: 10px;\n      }\n\n      > .timeline-body {\n        > img {\n          margin: 10px;\n        }\n        > dl,\n        ol,\n        ul {\n          margin: 0;\n        }\n      }\n\n      > .timeline-footer {\n        > a {\n          color: $white;\n        }\n      }\n    }\n    // The icons at line\n    > .fa,\n    > .fas,\n    > .far,\n    > .fab,\n    > .fal,\n    > .fad,\n    > .svg-inline--fa,\n    > .ion {\n      background-color: $gray-500;\n      border-radius: 50%;\n      font-size: 16px;\n      height: 30px;\n      left: 18px;\n      line-height: 30px;\n      position: absolute;\n      text-align: center;\n      top: 0;\n      width: 30px;\n    }\n    > .svg-inline--fa {\n      padding: 7px;\n    }\n  }\n  // Time label\n  > .time-label {\n    > span {\n      @include border-radius(4px);\n      background-color: $white;\n      display: inline-block;\n      font-weight: 600;\n      padding: 5px;\n    }\n  }\n}\n\n.timeline-inverse {\n  > div {\n    > .timeline-item {\n      @include box-shadow(none);\n      background-color: $gray-100;\n      border: 1px solid $gray-300;\n\n      > .timeline-header {\n        border-bottom-color: $gray-300;\n      }\n    }\n  }\n}\n\n.dark-mode {\n  .timeline {\n    &::before {\n      background-color: $gray-600;\n    }\n    > div > .timeline-item {\n      background-color: $dark;\n      color: $white;\n      border-color: $gray-600;\n\n      > .timeline-header {\n        color: $gray-400;\n        border-color: $gray-600;\n      }\n      > .time {\n        color: $gray-400;\n      }\n    }\n  }\n}\n", "//\n// Component: Products\n//\n\n.products-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n\n  > .item {\n    @include clearfix ();\n\n    @if $enable-rounded {\n      @include border-radius($border-radius);\n    }\n\n    background-color: $white;\n    padding: 10px 0;\n\n  }\n\n  .product-img {\n    float: left;\n\n    img {\n      height: 50px;\n      width: 50px;\n    }\n  }\n\n  .product-info {\n    margin-left: 60px;\n  }\n\n  .product-title {\n    font-weight: 600;\n  }\n\n  .product-description {\n    color: $gray-600;\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n}\n\n.product-list-in-card > .item {\n  @include border-radius(0);\n  border-bottom: 1px solid $card-border-color;\n\n  &:last-of-type {\n    border-bottom-width: 0;\n  }\n}\n\n\n.dark-mode {\n  .products-list > .item {\n    background-color: $dark;\n    color: $white;\n    border-bottom-color: $gray-600;\n  }\n\n  .product-description {\n    color: $gray-400;\n  }\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", "//\n// Component: Direct Chat\n//\n\n.direct-chat {\n  .card-body {\n    overflow-x: hidden;\n    padding: 0;\n    position: relative;\n  }\n\n  &.chat-pane-open {\n    .direct-chat-contacts {\n      @include translate(0, 0);\n    }\n  }\n\n\n  &.timestamp-light {\n    .direct-chat-timestamp {\n      color: lighten(color-yiq($yiq-text-light), 10%);\n    }\n  }\n\n  &.timestamp-dark {\n    .direct-chat-timestamp {\n      color: darken(color-yiq($yiq-text-dark), 20%);\n    }\n  }\n}\n\n.direct-chat-messages {\n  @include translate(0, 0);\n  height: 250px;\n  overflow: auto;\n  padding: 10px;\n}\n\n.direct-chat-msg,\n.direct-chat-text {\n  display: block;\n}\n\n.direct-chat-msg {\n  @include clearfix ();\n  margin-bottom: 10px;\n}\n\n.direct-chat-messages,\n.direct-chat-contacts {\n  transition: transform .5s ease-in-out;\n}\n\n.direct-chat-text {\n  @if $enable-rounded {\n    @include border-radius($border-radius-lg);\n  }\n\n  background-color: $direct-chat-default-msg-bg;\n  border: 1px solid $direct-chat-default-msg-border-color;\n  color: $direct-chat-default-font-color;\n  margin: 5px 0 0 50px;\n  padding: 5px 10px;\n  position: relative;\n\n  //Create the arrow\n  &::after,\n  &::before {\n    border: solid transparent;\n    border-right-color: $direct-chat-default-msg-border-color;\n    content: \" \";\n    height: 0;\n    pointer-events: none;\n    position: absolute;\n    right: 100%;\n    top: 15px;\n    width: 0;\n  }\n\n  &::after {\n    border-width: 5px;\n    margin-top: -5px;\n  }\n\n  &::before {\n    border-width: 6px;\n    margin-top: -6px;\n  }\n\n  .right & {\n    margin-left: 0;\n    margin-right: 50px;\n\n    &::after,\n    &::before {\n      border-left-color: $direct-chat-default-msg-border-color;\n      border-right-color: transparent;\n      left: 100%;\n      right: auto;\n    }\n  }\n}\n\n.direct-chat-img {\n  @include border-radius(50%);\n  float: left;\n  height: 40px;\n  width: 40px;\n\n  .right & {\n    float: right;\n  }\n}\n\n.direct-chat-infos {\n  display: block;\n  font-size: $font-size-sm;\n  margin-bottom: 2px;\n}\n\n.direct-chat-name {\n  font-weight: 600;\n}\n\n.direct-chat-timestamp {\n  color: darken($gray-500, 25%);\n}\n\n//Direct chat contacts pane\n.direct-chat-contacts-open {\n  .direct-chat-contacts {\n    @include translate(0, 0);\n  }\n}\n\n.direct-chat-contacts {\n  @include translate(101%, 0);\n  background-color: $dark;\n  bottom: 0;\n  color: $white;\n  height: 250px;\n  overflow: auto;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n\n.direct-chat-contacts-light {\n  background-color: $light;\n\n  .contacts-list-name {\n    color: $gray-700;\n  }\n\n  .contacts-list-date {\n    color: $gray-600;\n  }\n\n  .contacts-list-msg {\n    color: darken($gray-600, 10%);\n  }\n}\n\n//Contacts list -- for displaying contacts in direct chat contacts pane\n.contacts-list {\n  @include list-unstyled ();\n\n  > li {\n    @include clearfix ();\n    border-bottom: 1px solid rgba($black, .2);\n    margin: 0;\n    padding: 10px;\n\n    &:last-of-type {\n      border-bottom: 0;\n    }\n  }\n}\n\n.contacts-list-img {\n  @include border-radius(50%);\n  float: left;\n  width: 40px;\n}\n\n.contacts-list-info {\n  color: $white;\n  margin-left: 45px;\n}\n\n.contacts-list-name,\n.contacts-list-status {\n  display: block;\n}\n\n.contacts-list-name {\n  font-weight: 600;\n}\n\n.contacts-list-status {\n  font-size: $font-size-sm;\n}\n\n.contacts-list-date {\n  color: $gray-400;\n  font-weight: 400;\n}\n\n.contacts-list-msg {\n  color: darken($gray-400, 10%);\n}\n\n// Color variants\n@each $name, $color in $theme-colors {\n  .direct-chat-#{$name} {\n    @include direct-chat-variant($color);\n  }\n}\n\n@each $name, $color in $colors {\n  .direct-chat-#{$name} {\n    @include direct-chat-variant($color);\n  }\n}\n\n.dark-mode {\n  .direct-chat-text {\n    background-color: lighten($dark, 7.5%);\n    border-color: lighten($dark, 10%);\n    color: $white;\n\n    &::after,\n    &::before {\n      border-right-color: lighten($dark, 10%);\n    }\n  }\n  .direct-chat-timestamp {\n    color: $gray-500;\n  }\n  .right > .direct-chat-text {\n    &::after,\n    &::before {\n      border-right-color: transparent;\n    }\n  }\n\n  // Color variants\n  @each $name, $color in $theme-colors-alt {\n    .direct-chat-#{$name} {\n      @include direct-chat-variant($color);\n    }\n  }\n\n  @each $name, $color in $colors-alt {\n    .direct-chat-#{$name} {\n      @include direct-chat-variant($color);\n    }\n  }\n}\n", "//\n// Mixins: Miscellaneous\n//\n\n// ETC\n@mixin translate($x, $y) {\n  transform: translate($x, $y);\n}\n\n// Different radius each side\n@mixin border-radius-sides($top-left, $top-right, $bottom-left, $bottom-right) {\n  border-radius: $top-left $top-right $bottom-left $bottom-right;\n}\n\n@mixin calc($property, $expression) {\n  #{$property}: calc(#{$expression});\n}\n\n@mixin rotate($value) {\n  transform: rotate($value);\n}\n\n@mixin animation($animation) {\n  animation: $animation;\n}\n\n// Gradient background\n@mixin gradient($color: #f5f5f5, $start: #eee, $stop: $white) {\n  background-color: $color;\n  background-image: gradient(linear, left bottom, left top, color-stop(0, $start), color-stop(1, $stop));\n}\n\n@mixin scrollbar-width-thin() {\n  scrollbar-width: thin;\n  scrollbar-color: #a9a9a9 transparent;\n}\n\n@mixin scrollbar-width-none() {\n  scrollbar-width: none;\n}\n\n//\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "//\n// Mixins: Direct Chat\n//\n\n// Direct Chat Variant\n@mixin direct-chat-variant($bg-color, $color: $white) {\n  .right > .direct-chat-text {\n    background-color: $bg-color;\n    border-color: $bg-color;\n    color: color-yiq($bg-color);\n\n    &::after,\n    &::before {\n      border-left-color: $bg-color;\n    }\n  }\n}\n", "//\n// Core: Variables for Dark Mode\n//\n\n// COLORS\n// --------------------------------------------------------\n\n// stylelint-disable\n// Gray color will be default in dark mode\n$white-alt:    $white !default;\n$gray-100-alt: $gray-100 !default;\n$gray-200-alt: $gray-200 !default;\n$gray-300-alt: $gray-300 !default;\n$gray-400-alt: $gray-400 !default;\n$gray-500-alt: $gray-500 !default;\n$gray-600-alt: $gray-600 !default;\n$gray-700-alt: $gray-700 !default;\n$gray-800-alt: $gray-800 !default;\n$gray-900-alt: $gray-900 !default;\n$black-alt:    $black !default;\n\n$grays-alt: () !default;\n$grays-alt: map-merge((\n  \"100\": $gray-100-alt,\n  \"200\": $gray-200-alt,\n  \"300\": $gray-300-alt,\n  \"400\": $gray-400-alt,\n  \"500\": $gray-500-alt,\n  \"600\": $gray-600-alt,\n  \"700\": $gray-700-alt,\n  \"800\": $gray-800-alt,\n  \"900\": $gray-900-alt\n), $grays-alt);\n\n// Below colors from bootwatch darkly\n$blue-alt:    #3f6791 !default;\n$indigo-alt:  #6610f2 !default;\n$purple-alt:  #6f42c1 !default;\n$pink-alt:    #e83e8c !default;\n$red-alt:     #e74c3c !default;\n$orange-alt:  #fd7e14 !default;\n$yellow-alt:  #f39c12 !default;\n$green-alt:   #00bc8c !default;\n$teal-alt:    #20c997 !default;\n$cyan-alt:    #3498db !default;\n\n// by darken function\n$lightblue-alt: lighten(#3c8dbc, 20%) !default;\n$navy-alt: lighten(#001f3f, 5%) !default;\n$olive-alt: lighten(#3d9970, 20%) !default;\n$lime-alt: lighten(#01ff70, 20%) !default;\n$fuchsia-alt: lighten(#f012be, 20%) !default;\n$maroon-alt: lighten(#d81b60, 20%) !default;\n$gray-x-light-alt: lighten(#d2d6de, 20%) !default;\n\n$colors-alt: () !default;\n$colors-alt: map-merge((\n  \"blue\":       $blue-alt,\n  \"indigo\":     $indigo-alt,\n  \"purple\":     $purple-alt,\n  \"pink\":       $pink-alt,\n  \"red\":        $red-alt,\n  \"orange\":     $orange-alt,\n  \"yellow\":     $yellow-alt,\n  \"green\":      $green-alt,\n  \"teal\":       $teal-alt,\n  \"cyan\":       $cyan-alt,\n  \"white\":      $white-alt,\n  \"gray\":       $gray-600-alt,\n  \"gray-dark\":  $gray-800-alt\n), $colors-alt);\n\n$primary-alt:       $blue-alt !default;\n$secondary-alt:     $gray-600-alt !default;\n$success-alt:       $green-alt !default;\n$info-alt:          $cyan-alt !default;\n$warning-alt:       $yellow-alt !default;\n$danger-alt:        $red-alt !default;\n$light-alt:         $gray-100-alt !default;\n$dark-alt:          $gray-800-alt !default;\n\n$theme-colors-alt: () !default;\n$theme-colors-alt: map-merge((\n  \"primary\":    $primary-alt,\n  \"secondary\":  $secondary-alt,\n  \"success\":    $success-alt,\n  \"info\":       $info-alt,\n  \"warning\":    $warning-alt,\n  \"danger\":     $danger-alt,\n  \"light\":      $light-alt,\n  \"dark\":       $dark-alt\n), $theme-colors-alt);\n\n$colors-alt: map-merge(\n  (\n    \"lightblue\": $lightblue-alt,\n    \"navy\": $navy-alt,\n    \"olive\": $olive-alt,\n    \"lime\": $lime-alt,\n    \"fuchsia\": $fuchsia-alt,\n    \"maroon\": $maroon-alt,\n  ),\n  $colors-alt\n);\n// stylelint-enable\n\n//\n", "//\n// Component: Users List\n//\n\n.users-list {\n  @include list-unstyled ();\n\n  > li {\n    float: left;\n    padding: 10px;\n    text-align: center;\n    width: 25%;\n\n    img {\n      @include border-radius(50%);\n      height: auto;\n      max-width: 100%;\n    }\n\n    > a:hover {\n      &,\n      .users-list-name {\n        color: #999;\n      }\n    }\n  }\n}\n\n.users-list-name,\n.users-list-date {\n  display: block;\n}\n\n.users-list-name {\n  color: $gray-700;\n  font-size: $font-size-sm;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.users-list-date {\n  color: darken($gray-500, 20%);\n  font-size: 12px;\n}\n\n.dark-mode {\n  .users-list-name {\n    color: $gray-400;\n  }\n  .users-list-date {\n    color: $gray-500;\n  }\n}\n", "//\n// Component: Social Widgets\n//\n\n//General widget style\n.card-widget {\n  border: 0;\n  position: relative;\n}\n\n//User Widget Style 1\n.widget-user {\n\n  //User name container\n  .widget-user-header {\n    @if $enable-rounded {\n      @include border-top-radius($border-radius);\n    }\n\n    height: 135px;\n    padding: 1rem;\n    text-align: center;\n  }\n\n  //User name\n  .widget-user-username {\n    font-size: 25px;\n    font-weight: 300;\n    margin-bottom: 0;\n    margin-top: 0;\n    text-shadow: 0 1px 1px rgba($black, .2);\n  }\n\n  //User single line description\n  .widget-user-desc {\n    margin-top: 0;\n  }\n\n  //User image container\n  .widget-user-image {\n    left: 50%;\n    margin-left: -45px;\n    position: absolute;\n    top: 80px;\n\n    > img {\n      border: 3px solid $white;\n      height: auto;\n      width: 90px;\n    }\n  }\n\n  .card-footer {\n    padding-top: 50px;\n  }\n}\n\n//User Widget Style 2\n.widget-user-2 {\n\n  //User name container\n  .widget-user-header {\n    @include border-top-radius($border-radius);\n    padding: 1rem;\n  }\n\n  //User name\n  .widget-user-username {\n    font-size: 25px;\n    font-weight: 300;\n    margin-bottom: 5px;\n    margin-top: 5px;\n  }\n\n  //User single line description\n  .widget-user-desc {\n    margin-top: 0;\n  }\n\n  .widget-user-username,\n  .widget-user-desc {\n    margin-left: 75px;\n  }\n\n  //User image container\n  .widget-user-image {\n    > img {\n      float: left;\n      height: auto;\n      width: 65px;\n    }\n  }\n}\n"]}