<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection
include('../includes/config.php');
// Database connection is already included in config.php

// Test database connection
if (!$con) {
    die("Database connection failed: " . mysqli_connect_error());
}

// Test query to check if database is accessible
$test_query = mysqli_query($con, "SELECT 1");
if (!$test_query) {
    die("Database query failed: " . mysqli_error($con));
}

// Validating Session
if (!isset($_SESSION['aid']) || strlen($_SESSION['aid']) == 0) {
    header('location:../../../../php/pages/admin-login.php');
    exit();
}

  ?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Boat Booking System | Rejected Bookings</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
<?php include_once("../includes/navbar.php");?>
  <!-- /.navbar -->

 <?php include_once("../includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">


    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <!-- Modern Card Design -->
            <div class="card shadow-sm" style="border-radius: 10px; border: none; overflow: hidden;">
              <!-- Card Header with Rejected Bookings Information -->
              <div class="card-header bg-white" style="border-bottom: 1px solid #f0f0f0; padding: 1.5rem;">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h5 class="mb-0" style="font-weight: 600; color: #333;">
                      Rejected Bookings
                    </h5>
                    <p class="text-muted mb-0" style="font-size: 0.9rem;">
                      View cancelled and rejected booking requests
                    </p>
                  </div>
                  <div class="d-flex align-items-center">
                    <div class="bg-danger text-white d-flex align-items-center justify-content-center"
                         style="width: 80px; height: 80px; border-radius: 10px; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                      <div class="text-center">
                        <h2 class="mb-0" style="font-weight: 700; font-size: 1.8rem;">
                          <?php
                            // Count rejected bookings - try multiple possible status values
                            $count_query = "SELECT COUNT(*) as total FROM bookings WHERE booking_status IN ('cancelled', 'rejected', 'Cancelled', 'Rejected', 'CANCELLED', 'REJECTED', 'cancel', 'reject', 'Cancel', 'Reject')";
                            $count_result = $con->query($count_query);
                            $rejected_count = ($count_result && $count_result->num_rows > 0) ? $count_result->fetch_assoc()['total'] : 0;

                            // If still 0, show 0 (since rejected should be 0 if no rejections)
                            echo $rejected_count;
                          ?>
                        </h2>
                        <small style="font-size: 0.75rem; opacity: 0.9;">REJECTED</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Export Buttons -->
              <div class="bg-light py-2 px-3 border-bottom" style="border-color: #f0f0f0 !important;">
                <div class="btn-group">
                  <button class="btn btn-sm btn-outline-primary" id="copyToExcel">
                    <i class="fas fa-copy mr-1"></i> Copy and Paste to Excel
                  </button>
                  <button class="btn btn-sm btn-outline-primary" id="saveAsCsv">
                    <i class="fas fa-file-csv mr-1"></i> Save as CSV
                  </button>
                  <button class="btn btn-sm btn-outline-primary" id="saveAsExcel">
                    <i class="fas fa-file-excel mr-1"></i> Save as Excel
                  </button>
                  <button class="btn btn-sm btn-outline-primary" id="saveAsPdf">
                    <i class="fas fa-file-pdf mr-1"></i> Save as PDF
                  </button>
                  <button class="btn btn-sm btn-outline-primary" id="print">
                    <i class="fas fa-print mr-1"></i> Print
                  </button>
                  <button class="btn btn-sm btn-outline-primary" id="columnVisibility">
                    <i class="fas fa-columns mr-1"></i> Column Visibility
                  </button>
                </div>
                <div class="float-right">
                  <div class="input-group input-group-sm" style="width: 200px;">
                    <input type="text" id="tableSearch" class="form-control" placeholder="Search...">
                    <div class="input-group-append">
                      <button class="btn btn-outline-primary" type="button">
                        <i class="fas fa-search"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Table Content -->
              <div class="card-body p-0">
                <div class="table-responsive">
                  <table id="example1" class="table table-hover" style="border-collapse: separate; border-spacing: 0;">
                    <thead>
                      <tr style="background-color: #f8f9fa;">
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">#</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Reference No</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Customer Name</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Email</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Contact No</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Boat</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Destination</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Date</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Time</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Total Amount</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Status</th>
                        <th class="px-3 py-3" style="border-top: none; font-weight: 600; color: #495057; font-size: 0.9rem;">Action</th>
                      </tr>
                    </thead>
                    <tbody>
<?php
// First update any bookings with future dates (2025)
$update_sql = "UPDATE bookings
SET
    start_date = DATE_SUB(NOW(), INTERVAL 10 DAY),
    end_date = DATE_SUB(NOW(), INTERVAL 9 DAY),
    booking_time = DATE_SUB(NOW(), INTERVAL 12 DAY),
    created_at = DATE_SUB(NOW(), INTERVAL 13 DAY)
WHERE
    booking_status = 'cancelled' AND YEAR(created_at) = 2025";
$con->query($update_sql);

// Simple query to get all cancelled bookings using prepared statement
$sql = "SELECT
    b.booking_id,
    b.booking_code,
    b.start_date,
    b.booking_time,
    b.total,
    b.booking_status,
    c.first_name,
    c.last_name,
    c.email,
    c.contact_number,
    bt.name as boat_name,
    d.name as destination_name
    FROM bookings b
    JOIN customers c ON b.customer_id = c.customer_id
    JOIN boats bt ON b.boat_id = bt.boat_id
    LEFT JOIN destinations d ON b.destination_id = d.destination_id
    WHERE b.booking_status IN ('cancelled', 'rejected', 'Cancelled', 'Rejected', 'CANCELLED', 'REJECTED', 'cancel', 'reject', 'Cancel', 'Reject')
    ORDER BY b.created_at DESC";
$stmt = $con->prepare($sql);
$stmt->execute();
$query = $stmt->get_result();

if (!$query) {
    echo '<tr><td colspan="12" class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> Error loading bookings: ' . mysqli_error($con) . '</td></tr>';
} else {
    $cnt = 1;
    $hasRows = false;
    while($row = $query->fetch_assoc()) {
        $hasRows = true;
        $status = $row['booking_status'];
        $status_class = 'bg-danger';
        $status_text = 'Cancelled';

        // Format the booking time
        $booking_time = date('H:i', strtotime($row['booking_time']));

        echo '<tr style="border-bottom: 1px solid #f0f0f0;">';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; color: #666;">' . $cnt . '</td>';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; font-weight: 600; color: #333;">' . htmlspecialchars($row['booking_code']) . '</td>';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; color: #333;">';
        echo '<div class="d-flex align-items-center">';
        echo '<div class="bg-danger rounded-circle d-flex align-items-center justify-content-center mr-2" style="width: 32px; height: 32px; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">';
        echo '<i class="fas fa-user text-white" style="font-size: 0.8rem;"></i>';
        echo '</div>';
        echo '<span style="font-weight: 500;">' . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . '</span>';
        echo '</div>';
        echo '</td>';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; color: #666;">' . htmlspecialchars($row['email']) . '</td>';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; color: #666;">' . htmlspecialchars($row['contact_number']) . '</td>';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; color: #666;">' . htmlspecialchars($row['boat_name']) . '</td>';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; color: #666;">' . htmlspecialchars($row['destination_name']) . '</td>';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; color: #666;">' . date('M d, Y', strtotime($row['start_date'])) . '</td>';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; color: #666;">' . $booking_time . '</td>';
        echo '<td class="px-3 py-3" style="border: none; font-size: 0.9rem; font-weight: 600; color: #333;">&#8369; ' . number_format($row['total'], 2) . '</td>';
        echo '<td class="px-3 py-3" style="border: none;">';
        echo '<span style="background: #dc3545; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 500;">No</span>';
        echo '</td>';
        echo '<td class="px-3 py-3" style="border: none;">';
        echo '<div class="btn-group">';
        echo '<button class="btn btn-sm btn-outline-primary view-btn" data-id="' . $row['booking_id'] . '" title="View Details" style="border-radius: 4px; margin-right: 2px;">';
        echo '<i class="fas fa-eye" style="font-size: 0.8rem;"></i>';
        echo '</button>';
        echo '<a href="edit-booking.php?id=' . $row['booking_id'] . '" class="btn btn-sm btn-outline-warning" title="Edit Booking" style="border-radius: 4px; margin-right: 2px;">';
        echo '<i class="fas fa-edit" style="font-size: 0.8rem;"></i>';
        echo '</a>';
        echo '<button class="btn btn-sm btn-outline-danger delete-btn" data-id="' . $row['booking_id'] . '" title="Delete Booking" style="border-radius: 4px;">';
        echo '<i class="fas fa-trash" style="font-size: 0.8rem;"></i>';
        echo '</button>';
        echo '</div>';
        echo '</td>';
        echo '</tr>';
        $cnt++;
    }
    if (!$hasRows) {
        echo '<tr><td colspan="12" class="text-center"><i class="fas fa-info-circle"></i> No rejected or cancelled bookings found.</td></tr>';
    }
}
?>
                  </tbody>
                </table>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->
        </div>
        <!-- /.row -->
      </div>
      <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <!-- Delete Confirmation Modal -->
  <div class="modal fade" id="deleteConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header bg-danger">
          <h5 class="modal-title text-white" id="deleteConfirmationModalLabel">
            <i class="fas fa-exclamation-triangle"></i> Confirm Delete
          </h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="text-center">
            <i class="fas fa-trash-alt fa-3x mb-3 text-danger"></i>
            <p class="mb-0">Are you sure you want to delete this booking?</p>
            <p class="text-muted small">This action cannot be undone.</p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times"></i> Cancel
          </button>
          <button type="button" class="btn btn-danger" id="confirmDelete">
            <i class="fas fa-trash"></i> Delete
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Booking Details Modal -->
  <div class="modal fade" id="bookingDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookingDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header bg-info">
          <h5 class="modal-title text-white" id="bookingDetailsModalLabel">
            <i class="fas fa-info-circle"></i> Booking Details
          </h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-bordered table-striped" id="bookingDetailsTable">
              <tr><th>First Name:</th><td id="viewFirstName"></td></tr>
              <tr><th>Last Name:</th><td id="viewLastName"></td></tr>
              <tr><th>Age:</th><td id="viewAge"></td></tr>
              <tr><th>Sex:</th><td id="viewSex"></td></tr>
              <tr><th>Contact Number:</th><td id="viewContactNumber"></td></tr>
              <tr><th>Email:</th><td id="viewEmail"></td></tr>
              <tr><th>Address:</th><td id="viewAddress"></td></tr>
              <tr><th>Tour Destination:</th><td id="viewDestination"></td></tr>
              <tr><th>Number of Pax:</th><td id="viewNoOfPax"></td></tr>
              <tr><th>Start Date:</th><td id="viewStartDate"></td></tr>
              <tr><th>End Date:</th><td id="viewEndDate"></td></tr>
              <tr><th>Duration:</th><td id="viewDuration"></td></tr>
              <tr><th>Booking Time:</th><td id="viewBookingTime"></td></tr>
              <tr><th>Selected Boat:</th><td id="viewBoat"></td></tr>
              <tr><th>Boat Price:</th><td id="viewBoatPrice"></td></tr>
              <tr><th>Booking Code:</th><td id="viewBookingCode"></td></tr>
              <tr><th>Environmental Fee:</th><td id="viewEnvironmentalFee"></td></tr>
              <tr><th>Payment Method:</th><td id="viewPaymentMethod"></td></tr>
              <tr><th>Total Amount:</th><td id="viewTotal"></td></tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Booking Modal -->
  <div class="modal fade" id="editBookingModal" tabindex="-1" role="dialog" aria-labelledby="editBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header bg-primary">
          <h5 class="modal-title text-white" id="editBookingModalLabel">
            <i class="fas fa-edit"></i> Edit Booking
          </h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <form id="editBookingForm">
            <input type="hidden" name="booking_id" id="editBookingId">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>Booking Code</label>
                  <input type="text" class="form-control" id="editBookingCode" readonly>
                </div>
                <div class="form-group">
                  <label>Customer Name</label>
                  <input type="text" class="form-control" id="editCustomerName" readonly>
                </div>
                <div class="form-group">
                  <label>Email</label>
                  <input type="email" class="form-control" id="editEmail" readonly>
                </div>
                <div class="form-group">
                  <label>Contact Number</label>
                  <input type="text" class="form-control" id="editContactNumber" readonly>
                </div>
                <div class="form-group">
                  <label>Address</label>
                  <input type="text" class="form-control" id="editAddress" name="address">
                </div>
                <div class="form-group">
                  <label>Age</label>
                  <input type="number" class="form-control" id="editAge" name="age" min="1" max="120">
                </div>
                <div class="form-group">
                  <label>Sex</label>
                  <select class="form-control" id="editSex" name="sex">
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>Boat</label>
                  <select class="form-control" id="editBoat" name="boat_id" required>
                    <option value="">Select Boat</option>
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>Destination</label>
                  <select class="form-control" id="editDestination" name="destination_id" required>
                    <option value="">Select Destination</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>Start Date</label>
                  <input type="date" class="form-control" id="editStartDate" name="start_date" required>
                </div>
                <div class="form-group">
                  <label>End Date</label>
                  <input type="date" class="form-control" id="editEndDate" name="end_date" required>
                </div>
                <div class="form-group">
                  <label>Booking Time</label>
                  <input type="time" class="form-control" id="editBookingTime" name="booking_time" required>
                </div>
                <div class="form-group">
                  <label>Total Amount</label>
                  <input type="number" class="form-control" id="editTotal" name="total" required>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label>Number of Pax</label>
                  <input type="number" class="form-control" id="editNoOfPax" name="no_of_pax" required>
                </div>
                <div class="form-group">
                  <label>Environmental Fee</label>
                  <input type="number" step="0.01" class="form-control" id="editEnvironmentalFee" name="environmental_fee">
                </div>
                <div class="form-group">
                  <label>Payment Method</label>
                  <select class="form-control" id="editPaymentMethod" name="payment_method">
                    <option value="gcash">GCash</option>
                    <option value="manual">Manual Payment</option>
                  </select>
                </div>


                <div class="form-group">
                  <label>Booking Status</label>
                  <select class="form-control" id="editStatus" name="booking_status">
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="saveEdit">Save Changes</button>
        </div>
      </div>
    </div>
  </div>

<?php include_once('../includes/footer.php');?>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- DataTables  & Plugins -->
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="dist/js/demo.js"></script>
<!-- Page specific script -->
<script>
  $(function () {
    // Check if DataTable is already initialized
    if ($.fn.dataTable.isDataTable('#example1')) {
      // If already initialized, destroy it first
      $('#example1').DataTable().destroy();
    }

    // Initialize DataTable
    var table = $("#example1").DataTable({
      "responsive": true,
      "lengthChange": false,
      "autoWidth": false,
      "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
    });

    // Connect custom buttons to DataTable functionality
    $('#copyToExcel').on('click', function() {
      table.button('.buttons-copy').trigger();
    });

    $('#saveAsCsv').on('click', function() {
      table.button('.buttons-csv').trigger();
    });

    $('#saveAsExcel').on('click', function() {
      table.button('.buttons-excel').trigger();
    });

    $('#saveAsPdf').on('click', function() {
      table.button('.buttons-pdf').trigger();
    });

    $('#print').on('click', function() {
      table.button('.buttons-print').trigger();
    });

    $('#columnVisibility').on('click', function() {
      table.button('.buttons-colvis').trigger();
    });

    // Connect search box to DataTable
    $('#tableSearch').keyup(function() {
      table.search($(this).val()).draw();
    });

    // Hide the default buttons container
    $('.dt-buttons').hide();

    // View booking details - when view button is clicked
    $(document).on('click', '.view-btn', function() {
      // Get booking ID from the button
      var id = $(this).data('id');

      // Get booking details from server
      $.post('get-booking-details.php', { id: id }, function(response) {
        if(response.success && response.data) {
          var booking = response.data;

          // Format dates for display
          var startDate = new Date(booking.start_date);
          var endDate = new Date(booking.end_date);

          // Calculate duration between dates
          var days = Math.floor((endDate - startDate) / (24 * 60 * 60 * 1000));
          var durationStr = days + ' day(s)';

          // Format currency values
          function formatPeso(amount) {
            return '₱ ' + parseFloat(amount).toFixed(2);
          }

          // Fill in the details in the modal
          $('#viewFirstName').text(booking.first_name || 'Not set');
          $('#viewLastName').text(booking.last_name || 'Not set');
          $('#viewContactNumber').text(booking.contact_number || 'Not set');
          $('#viewEmail').text(booking.email || 'Not set');
          $('#viewDestination').text(booking.destination_name || 'Not set');
          $('#viewNoOfPax').text(booking.no_of_pax || 'Not set');
          $('#viewStartDate').text(startDate.toLocaleDateString());
          $('#viewEndDate').text(endDate.toLocaleDateString());
          $('#viewDuration').text(durationStr);
          $('#viewBookingTime').text(booking.booking_time || 'Not set');
          $('#viewBoat').text(booking.boat_name || 'Not set');
          $('#viewBookingCode').text(booking.booking_code || 'Not set');
          $('#viewTotal').text(formatPeso(booking.total));

          // Show the modal with booking details
          $('#bookingDetailsModal').modal('show');
        } else {
          alert('Could not load booking details');
        }
      }, 'json').fail(function() {
        alert('Error connecting to server');
      });
    });

    // Edit booking - when edit button is clicked
    $(document).on('click', '.edit-btn', function() {
      // Get the booking ID from the button's data attribute
      var id = $(this).data('id');

      // Validate booking ID
      if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
        alert('Invalid booking ID. Please try again or contact support.');
        console.error('Invalid booking ID detected:', id);
        return;
      }

      // Reset form fields
      $('#editBookingForm')[0].reset();

      // First load the dropdown options for boats and destinations
      loadBoatsAndDestinations();

      // Show loading indicator in the modal
      var originalModalContent = $('#editBookingModal .modal-body').html();
      $('#editBookingModal .modal-body').html('<div class="text-center p-5"><i class="fas fa-spinner fa-spin fa-3x"></i><p class="mt-3">Loading booking details...</p></div>');
      $('#editBookingModal').modal('show');

      // Get booking details from server
      $.post('get-booking-details.php', { id: id }, function(response) {
        if(response.success && response.data) {
          var booking = response.data;

          console.log("Booking data received:", booking); // Debug log

          // Restore the form content
          $('#editBookingModal .modal-body').html(originalModalContent);

          // Fill in the form with booking data
          $('#editBookingId').val(booking.booking_id);
          $('#editBookingCode').val(booking.booking_code || 'N/A');
          $('#editCustomerName').val((booking.first_name || '') + ' ' + (booking.last_name || ''));
          $('#editEmail').val(booking.email || '');
          $('#editContactNumber').val(booking.contact_number || '');
          $('#editAddress').val(booking.address || '');
          $('#editAge').val(booking.age || '');
          $('#editSex').val(booking.sex || 'Male');
          $('#editNoOfPax').val(booking.no_of_pax || 1);
          $('#editEnvironmentalFee').val(booking.environmental_fee || 0);
          $('#editDropoffLocation').val(booking.dropoff_location || '');

          // Set dropdown values after a short delay to ensure they're loaded
          setTimeout(function() {
            $('#editBoat').val(booking.boat_id);
            $('#editDestination').val(booking.destination_id);
            $('#editPaymentMethod').val(booking.payment_method || 'cash');
            $('#editStatus').val(booking.booking_status || 'cancelled');
          }, 500);

          // Set date and time fields
          if (booking.start_date && booking.start_date !== 'N/A') {
            $('#editStartDate').val(booking.start_date);
          }

          if (booking.end_date && booking.end_date !== 'N/A') {
            $('#editEndDate').val(booking.end_date);
          }

          if (booking.booking_time && booking.booking_time !== 'N/A') {
            $('#editBookingTime').val(booking.booking_time);
          }

          $('#editTotal').val(booking.total || 0);
        } else {
          // Show more detailed error message
          var errorMsg = response.error || 'Unknown error occurred';
          $('#editBookingModal .modal-body').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Could not load booking details: ' + errorMsg + '</div>');
          console.error('Error loading booking details:', response);
        }
      }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
        $('#editBookingModal .modal-body').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Error connecting to server. Please try again later.</div>');
        console.error('AJAX error during edit:', textStatus, errorThrown);
      });
    });

    // Delete booking - when delete button is clicked
    $('.delete-btn').click(function() {
      // Get booking ID from the button
      var id = $(this).data('id');

      // Validate booking ID
      if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
        alert('Invalid booking ID. Cannot delete this booking.');
        console.error('Invalid booking ID detected for delete:', id);
        return;
      }

      // First verify the booking exists before showing delete confirmation
      $.post('get-booking-details.php', { id: id }, function(response) {
        if(response.success && response.data) {
          // Booking exists, show confirmation modal
          $('#deleteConfirmationModal').modal('show');
          $('#confirmDelete').data('booking-id', id);
        } else {
          alert('Error: ' + (response.error || 'Booking not found'));
          console.error('Booking verification error:', response);
        }
      }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
        alert('Error connecting to server. Please try again later.');
        console.error('AJAX error during delete verification:', textStatus, errorThrown);
      });
    });

    // When delete is confirmed
    $('#confirmDelete').click(function() {
      var id = $(this).data('booking-id');

      // Validate booking ID again as a safeguard
      if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
        alert('Invalid booking ID. Cannot delete this booking.');
        $('#deleteConfirmationModal').modal('hide');
        return;
      }

      // Send delete request to server
      $.post('delete-booking.php', { id: id }, function(response) {
        if(response.success) {
          $('#deleteConfirmationModal').modal('hide');
          alert('Booking deleted successfully');
          location.reload(); // Refresh the page
        } else {
          alert(response.error || 'Error deleting booking');
          console.error('Delete booking error:', response);
        }
      }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
        alert('Error connecting to server. Please try again later.');
        console.error('AJAX error during delete:', textStatus, errorThrown);
        $('#deleteConfirmationModal').modal('hide');
      });
    });

    // Save edited booking
    $('#saveEdit').click(function() {
      // Get all form data as a serialized string
      var formData = $('#editBookingForm').serialize();

      // Add customer name to form data
      var customerName = $('#editCustomerName').val();
      formData += '&customer_name=' + encodeURIComponent(customerName);

      // Add destination name to form data
      var destinationId = $('#editDestination').val();
      var destinationName = $('#editDestination option:selected').text();
      formData += '&destination_name=' + encodeURIComponent(destinationName);

      // Add boat name to form data
      var boatId = $('#editBoat').val();
      var boatName = $('#editBoat option:selected').text();
      formData += '&boat_name=' + encodeURIComponent(boatName);

      console.log('Form data with added fields:', formData);

      // Basic validation for required fields
      var requiredFields = ['boat_id', 'destination_id', 'start_date', 'end_date',
                           'booking_time', 'no_of_pax', 'total'];

      var missingFields = [];
      requiredFields.forEach(function(field) {
        var value = $('[name="' + field + '"]').val();
        if (!value) {
          missingFields.push(field.replace('_', ' '));
        }
      });

      if (missingFields.length > 0) {
        alert('Please fill in the following required fields: ' + missingFields.join(', '));
        return;
      }

      // Validate dates
      var startDate = new Date($('#editStartDate').val());
      var endDate = new Date($('#editEndDate').val());

      if (endDate < startDate) {
        alert('End date cannot be before start date');
        return;
      }

      // Show loading state
      $('#saveEdit').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Saving...');

      // Send data to server
      $.ajax({
        url: 'update-booking.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
          if(response.success) {
            $('#editBookingModal').modal('hide');
            alert('Booking updated successfully');
            location.reload();
          } else {
            $('#saveEdit').prop('disabled', false).html('Save Changes');
            alert(response.error || 'Error updating booking');
            console.error('Update error:', response);
          }
        },
        error: function(jqXHR, textStatus, errorThrown) {
          $('#saveEdit').prop('disabled', false).html('Save Changes');
          alert('Error connecting to server. Please try again later.');
          console.error('AJAX error:', textStatus, errorThrown);

          // Try to parse response for more details
          try {
            var errorResponse = JSON.parse(jqXHR.responseText);
            console.error('Server error details:', errorResponse);
          } catch(e) {
            console.error('Raw server response:', jqXHR.responseText);
          }
        }
      });
    });

    // Function to load boats and destinations for the dropdown menus
    function loadBoatsAndDestinations() {
      // Load boats
      $.get('get-options.php', { type: 'boats' }, function(data) {
        // Add default option
        var boatOptions = '<option value="">Select Boat</option>' + data;
        $('#editBoat').html(boatOptions);
      }).fail(function(jqXHR, textStatus, errorThrown) {
        console.error('Error loading boats:', textStatus, errorThrown);
        $('#editBoat').html('<option value="">Error loading boats</option>');
      });

      // Load destinations
      $.get('get-options.php', { type: 'destinations' }, function(data) {
        // Add default option
        var destOptions = '<option value="">Select Destination</option>' + data;
        $('#editDestination').html(destOptions);
      }).fail(function(jqXHR, textStatus, errorThrown) {
        console.error('Error loading destinations:', textStatus, errorThrown);
        $('#editDestination').html('<option value="">Error loading destinations</option>');
      });
    }
  });
</script>
</body>
</html>