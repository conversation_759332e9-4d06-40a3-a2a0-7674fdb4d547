  <!-- Navbar -->
  <nav class="main-header navbar navbar-expand navbar-white navbar-light" style="margin-bottom: 0; padding: 0.5rem 0.5rem 0.5rem 1rem;">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
      <li class="nav-item">
        <button class="nav-link" data-widget="pushmenu" type="button" role="button" style="background:none;border:none;"><i class="fas fa-bars"></i></button>
      </li>
    </ul>

    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">
      <?php
      // Include database connection
      include_once __DIR__ . '/config.php';
      // Fetch count of unread notifications
      $admin_id = intval($_SESSION['aid']);
      $notifStmt = $con->prepare("SELECT COUNT(*) as cnt FROM notifications WHERE user_id = ? AND is_read = 0");
      $notifStmt->bind_param("i", $admin_id);
      $notifStmt->execute();
      $notifResult = $notifStmt->get_result();
      $notifCount = $notifResult->fetch_assoc()['cnt'];
      $notifStmt->close();
      ?>
      <li class="nav-item">
        <a class="nav-link" href="#" id="notifBell" role="button" data-toggle="modal" data-target="#notifModal">
          <i class="fas fa-bell"></i>
          <?php if ($notifCount > 0): ?>
          <span class="badge badge-danger navbar-badge" id="notifCount"><?php echo $notifCount; ?></span>
          <?php endif; ?>
        </a>
      </li>

      <li class="nav-item">
        <a class="nav-link" data-widget="fullscreen" href="#" role="button">
          <i class="fas fa-expand-arrows-alt"></i>
        </a>
      </li>

      <!-- Role Indicator removed -->
    </ul>
  </nav>
  <!-- /.navbar -->

  <!-- Notifications Modal -->
  <div class="modal fade" id="notifModal" tabindex="-1" role="dialog" aria-labelledby="notifModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header bg-primary">
          <h5 class="modal-title text-white" id="notifModalLabel">
            <i class="fas fa-bell mr-2"></i>Notifications
          </h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body p-0">
          <div class="list-group" id="notifList">
            <!-- Notifications will be loaded here -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Notification Scripts -->
  <script>
  // Wait for jQuery to be fully loaded
  document.addEventListener('DOMContentLoaded', function() {
    // Make sure jQuery is available
    if (typeof jQuery !== 'undefined') {
      $(document).ready(function() {
        function loadNotifications() {
          $.get('notifications_fetch.php', function(data) {
            try {
              let response = typeof data === 'string' ? JSON.parse(data) : data;

              // Check if there's an error in the response
              if (response.error) {
                console.error("Server reported an error:", response.message);
                $('#notifList').html('<div class="alert alert-danger">' + response.message + '</div>');
                return;
              }

              let notifs = response;
              let notifList = $('#notifList');
              let notifCount = notifs.length;

              // Update notification count
              if (notifCount > 0) {
                // If count is greater than 0, show the badge with the count
                if ($('#notifCount').length) {
                  $('#notifCount').text(notifCount);
                } else {
                  // If the badge doesn't exist, create it
                  $('#notifBell i').after('<span class="badge badge-danger navbar-badge" id="notifCount">' + notifCount + '</span>');
                }
              } else {
                // If count is 0, remove the badge
                $('#notifCount').remove();
              }

              // Clear existing notifications
              notifList.empty();

              if (notifs.length === 0) {
                notifList.append('<div class="text-center p-3">No new notifications</div>');
              } else {
                notifs.forEach(function(notif) {
                  // Format the notification message for better display
                  let message = notif.message || 'New notification';

                  // If message is too long, truncate it and add ellipsis
                  const maxLength = 100;
                  const shortMessage = message.length > maxLength ?
                                      message.substring(0, maxLength) + '...' :
                                      message;

                  // Extract customer name from notification message if it exists
                  let customerName = '';
                  let bookingCode = '';

                  // Try to extract customer name using regex
                  const nameRegex = /from\s+([^(]+?)(?:\s+for|\s+\(|$)/i;
                  const nameMatch = message.match(nameRegex);
                  if (nameMatch && nameMatch[1]) {
                    customerName = nameMatch[1].trim();
                  }

                  // Try to extract booking code using regex
                  const codeRegex = /#([A-Z0-9-]+)/i;
                  const codeMatch = message.match(codeRegex);
                  if (codeMatch && codeMatch[1]) {
                    bookingCode = codeMatch[1];
                  }

                  // Create HTML for the notification with customer name highlighted
                  let notifHtml = `
                    <div class="list-group-item list-group-item-action" onclick="markAsReadAndGo(${notif.id})" style="cursor:pointer;">
                      <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">
                          ${customerName ?
                            `<span class="badge badge-info mr-1"><i class="fas fa-user mr-1"></i>${customerName}</span> ` :
                            ''}
                          ${bookingCode ?
                            `<span class="badge badge-secondary mr-1">#${bookingCode}</span> ` :
                            ''}
                          ${shortMessage}
                        </h6>
                        <small class="text-muted">${new Date(notif.created_at).toLocaleString()}</small>
                      </div>
                      <div class="mt-2 d-flex justify-content-between align-items-center">
                        <small class="text-primary"><i class="fas fa-calendar-day mr-1"></i>Today's Bookings</small>
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); viewFullNotification(${JSON.stringify(message)})">
                          <i class="fas fa-eye"></i> View Details
                        </button>
                      </div>
                    </div>
                  `;
                  notifList.append(notifHtml);
                });
              }
            } catch (error) {
              console.error("Error parsing notifications:", error);
              $('#notifList').html('<div class="alert alert-danger">Error loading notifications</div>');
            }
          }).fail(function(jqXHR, textStatus, errorThrown) {
            console.error("AJAX error:", textStatus, errorThrown);
            $('#notifList').html('<div class="alert alert-danger">Failed to load notifications</div>');
          });
        }

        // Load notifications when modal is shown
        $('#notifModal').on('show.bs.modal', loadNotifications);

        // Refresh notifications every 30 seconds
        setInterval(loadNotifications, 30000);
      });
    } else {
      console.error("jQuery is not loaded. Notifications will not work.");
    }
  });

  function markAsReadAndGo(id) {
    if (typeof jQuery !== 'undefined') {
      $.get('notifications_mark_read.php?id=' + id, function(response) {
        try {
          if (response.success) {
            window.location.href = 'all-booking.php?filter=today';
          } else {
            alert('Error marking notification as read');
          }
        } catch (error) {
          console.error("Error processing response:", error);
          alert('Error processing notification');
        }
      }, 'json').fail(function() {
        alert('Failed to mark notification as read');
      });
    } else {
      alert('System error: jQuery not loaded');
    }
  }

  function backToNotifications() {
    // Hide the full notification modal
    $('#fullNotifModal').modal('hide');

    // Clean up any lingering backdrops
    setTimeout(function() {
      $('.modal-backdrop').remove();
      $('body').removeClass('modal-open');
      $('body').css('padding-right', '');
      $('body').css('overflow', '');

      // Show the notifications modal
      setTimeout(function() {
        $('#notifModal').modal('show');
      }, 100);
    }, 300);
  }

  function viewFullNotification(message) {
    // Create a modal to display the full notification
    if (typeof jQuery !== 'undefined') {
      console.log("Showing full notification:", message);

      // Hide the notifications modal first
      $('#notifModal').modal('hide');

      // Remove any existing backdrops
      $('.modal-backdrop').remove();

      // Wait for the first modal to be fully hidden
      setTimeout(function() {
        // Extract customer name from notification message if it exists
        let customerName = '';
        let bookingCode = '';

        // Try to extract customer name using regex
        const nameRegex = /from\s+([^(]+?)(?:\s+for|\s+\(|$)/i;
        const nameMatch = message.match(nameRegex);
        if (nameMatch && nameMatch[1]) {
          customerName = nameMatch[1].trim();
        }

        // Try to extract booking code using regex
        const codeRegex = /#([A-Z0-9-]+)/i;
        const codeMatch = message.match(codeRegex);
        if (codeMatch && codeMatch[1]) {
          bookingCode = codeMatch[1];
        }

        // Format the message with customer details highlighted
        let formattedMessage = message;

        // Create a new modal for the full notification
        const modalHtml = `
          <div class="modal fade" id="fullNotifModal" tabindex="-1" role="dialog" aria-labelledby="fullNotifModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-header bg-primary">
                  <h5 class="modal-title text-white" id="fullNotifModalLabel">
                    <i class="fas fa-info-circle mr-2"></i>Booking Details
                    ${customerName ? `<span class="badge badge-light ml-2"><i class="fas fa-user mr-1"></i>${customerName}</span>` : ''}
                    ${bookingCode ? `<span class="badge badge-light ml-2">#${bookingCode}</span>` : ''}
                  </h5>
                  <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                </div>
                <div class="modal-body">
                  <div class="card">
                    <div class="card-body">
                      ${customerName ?
                        `<div class="alert alert-info">
                          <strong><i class="fas fa-user-circle mr-1"></i> Customer:</strong> ${customerName}
                        </div>` : ''}
                      <pre style="white-space: pre-wrap; word-break: break-word;">${message}</pre>
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                  <button type="button" class="btn btn-primary" onclick="backToNotifications()">
                    Back to Notifications
                  </button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Remove any existing modal with the same ID
        $('#fullNotifModal').remove();

        // Append the modal to the body
        $('body').append(modalHtml);

        // Show the modal
        $('#fullNotifModal').modal('show');

        // Add event handler for when this modal is hidden
        $('#fullNotifModal').on('hidden.bs.modal', function() {
          // Remove any lingering backdrops
          $('.modal-backdrop').remove();

          // Remove modal-open class from body
          $('body').removeClass('modal-open');

          // Remove inline styles added by Bootstrap
          $('body').css('padding-right', '');
          $('body').css('overflow', '');
        });
      }, 300);
    } else {
      // Fallback if jQuery or Bootstrap is not available
      alert(message);
    }
  }
  </script>

  <!-- Update user activity status -->
  <script>
  document.addEventListener('DOMContentLoaded', function() {
    // Make sure jQuery is available
    if (typeof jQuery !== 'undefined') {
      $(document).ready(function() {
        // Function to update last_activity timestamp
        function updateActivity() {
          $.ajax({
            url: 'update_activity.php',
            type: 'POST',
            dataType: 'json',
            success: function(response) {
              if (response.success) {
                console.log('Activity updated successfully');
              }
            },
            error: function() {
              console.error('Failed to update activity status');
            }
          });
        }

        // Update activity on page load
        updateActivity();

        // Update activity every 5 minutes
        setInterval(updateActivity, 300000); // 5 minutes in milliseconds

        // Also update on user interaction
        $(document).on('click keypress scroll', function() {
          updateActivity();
        });
      });
    }
  });
  </script>
