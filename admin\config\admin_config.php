<?php
/**
 * Admin System Configuration
 * 
 * This file contains configuration settings specific to the admin system.
 * It includes database connections, security settings, and admin-specific constants.
 */

// Prevent direct access
if (!defined('ADMIN_SYSTEM')) {
    die('Direct access not permitted');
}

// Admin system paths
define('ADMIN_PATH', __DIR__ . '/../');
define('MAIN_SYSTEM_PATH', __DIR__ . '/../../');

// Database configuration for admin system
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'booking_system');

// Create database connection for admin system
$con = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($con->connect_error) {
    die("Connection failed: " . $con->connect_error);
}
$con->set_charset("utf8");

// Admin session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

// Admin session timeout (30 minutes)
define('ADMIN_SESSION_TIMEOUT', 1800);

// CSRF token configuration
define('CSRF_TOKEN_NAME', 'admin_csrf_token');

// Admin login attempt limits
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Admin system version
define('ADMIN_SYSTEM_VERSION', '1.0.0');

// Function to check admin authentication
function requireAdminAuth() {
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        header('Location: ' . ADMIN_BASE_URL . 'pages/login.php');
        exit;
    }
    
    // Check session timeout
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > ADMIN_SESSION_TIMEOUT) {
        session_destroy();
        header('Location: ' . ADMIN_BASE_URL . 'pages/login.php?timeout=1');
        exit;
    }
    
    // Update last activity
    $_SESSION['last_activity'] = time();
}

// Function to generate CSRF token
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// Function to verify CSRF token
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}
?>
