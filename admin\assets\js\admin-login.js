/**
 * Admin Login JavaScript
 * 
 * JavaScript functionality for the admin login page
 */

// Toggle password visibility
function togglePassword() {
    const pwd = document.getElementById('password');
    const eye = document.getElementById('eyeIcon');
    
    if (pwd.type === 'password') {
        pwd.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        pwd.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.login-container');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    // Add form validation
    form.addEventListener('submit', function(e) {
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!username || !password) {
            e.preventDefault();
            showError('Please enter both username and password.');
            return false;
        }
        
        if (username.length < 3) {
            e.preventDefault();
            showError('Username must be at least 3 characters long.');
            return false;
        }
        
        if (password.length < 6) {
            e.preventDefault();
            showError('Password must be at least 6 characters long.');
            return false;
        }
    });
    
    // Auto-hide messages after 5 seconds
    const errorMsg = document.querySelector('.error-msg');
    const successMsg = document.querySelector('.success-msg');
    
    if (errorMsg) {
        setTimeout(() => {
            errorMsg.style.opacity = '0';
            setTimeout(() => errorMsg.remove(), 300);
        }, 5000);
    }
    
    if (successMsg) {
        setTimeout(() => {
            successMsg.style.opacity = '0';
            setTimeout(() => successMsg.remove(), 300);
        }, 3000);
    }
});

// Show error message
function showError(message) {
    // Remove existing error messages
    const existingError = document.querySelector('.error-msg');
    if (existingError) {
        existingError.remove();
    }
    
    // Create new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-msg';
    errorDiv.innerHTML = '<i class="fas fa-exclamation-circle"></i> ' + message;
    
    // Insert before the first input group
    const firstInputGroup = document.querySelector('.input-group');
    firstInputGroup.parentNode.insertBefore(errorDiv, firstInputGroup);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        errorDiv.style.opacity = '0';
        setTimeout(() => errorDiv.remove(), 300);
    }, 5000);
}

// Prevent multiple form submissions
let isSubmitting = false;
document.querySelector('.login-container').addEventListener('submit', function() {
    if (isSubmitting) {
        return false;
    }
    isSubmitting = true;
    
    const submitBtn = document.querySelector('.login-btn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
    
    // Re-enable after 10 seconds as fallback
    setTimeout(() => {
        isSubmitting = false;
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'Login as Administrator';
    }, 10000);
});

// Security: Clear form data on page unload
window.addEventListener('beforeunload', function() {
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
});
