<?php
/**
 * Admin System Configuration
 * Updated for the new admin system structure
 */

// Prevent direct access
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}

//time zone
date_default_timezone_set('Asia/Manila');

// Admin system paths (only define if not already defined)
if (!defined('ADMIN_ROOT')) {
    define('ADMIN_ROOT', __DIR__ . '/../');
}
if (!defined('MAIN_SYSTEM_PATH')) {
    define('MAIN_SYSTEM_PATH', __DIR__ . '/../../website/');
}
if (!defined('ADMIN_BASE_URL')) {
    define('ADMIN_BASE_URL', '/Online Booking Reservation System/admin/');
}

// Define database constants
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'booking_system');

// Create database connection for admin system
// Include main system database connection if it exists, otherwise create our own
if (file_exists(MAIN_SYSTEM_PATH . 'config/db_connect.php')) {
    require_once MAIN_SYSTEM_PATH . 'config/db_connect.php';
} else {
    // Create our own database connection
    $con = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($con->connect_error) {
        die("Connection failed: " . $con->connect_error);
    }
    $con->set_charset("utf8");
}

// Function to create database connection with error handling
function createDatabaseConnection() {
    try {
        // Set timeout to prevent long waits
        $options = [
            MYSQLI_OPT_CONNECT_TIMEOUT => 5
        ];

        // Create connection
        $connection = mysqli_init();

        // Set options
        foreach ($options as $option => $value) {
            $connection->options($option, $value);
        }

        // Connect to database
        $connection->real_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);

        // Check for connection errors
        if ($connection->connect_error) {
            throw new Exception("Connection failed: " . $connection->connect_error);
        }

        // Set character set
        $connection->set_charset("utf8");

        return $connection;
    } catch (Exception $e) {
        // Log the error
        error_log("Database connection error: " . $e->getMessage());

        // For admin pages, show a user-friendly error
        if (strpos($_SERVER['PHP_SELF'], 'admin') !== false) {
            echo "<div style='margin: 50px auto; max-width: 600px; padding: 20px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; color: #721c24;'>";
            echo "<h3>Database Connection Error</h3>";
            echo "<p>We're having trouble connecting to the database. This could be because:</p>";
            echo "<ul>";
            echo "<li>The MySQL server is not running</li>";
            echo "<li>The database credentials are incorrect</li>";
            echo "<li>The server is experiencing high load</li>";
            echo "</ul>";
            echo "<p>Please try the following:</p>";
            echo "<ul>";
            echo "<li>Refresh the page</li>";
            echo "<li>Check if MySQL service is running</li>";
            echo "<li>Contact the system administrator</li>";
            echo "</ul>";
            echo "</div>";
        }

        return false;
    }
}

// Create database connection
$con = createDatabaseConnection();

// Session start only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Function to check if admin is logged in
function checkLogin() {
    if (!isset($_SESSION['aid']) || empty($_SESSION['aid'])) {
        header("Location: " . ADMIN_BASE_URL . "pages/login.php");
        exit();
    }
}

// Function to require admin authentication
function requireAdminAuth() {
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        header('Location: ' . ADMIN_BASE_URL . 'pages/login.php');
        exit;
    }

    // Check session timeout
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > 1800) {
        session_destroy();
        header('Location: ' . ADMIN_BASE_URL . 'pages/login.php?timeout=1');
        exit;
    }

    // Update last activity
    $_SESSION['last_activity'] = time();
}

// Function to get admin details
function getAdminDetails($admin_id) {
    global $con;

    // Check if connection is valid
    if (!$con) {
        return false;
    }

    try {
        $stmt = $con->prepare("SELECT * FROM admins WHERE admin_id = ?");
        if (!$stmt) {
            return false;
        }

        $stmt->bind_param("i", $admin_id);
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_assoc();
    } catch (Exception $e) {
        error_log("Error getting admin details: " . $e->getMessage());
        return false;
    }
}

?>
