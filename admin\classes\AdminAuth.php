<?php
/**
 * Admin Authentication Class
 * 
 * Handles admin authentication, session management, and security for the admin system.
 */

// Prevent direct access
if (!defined('ADMIN_SYSTEM')) {
    die('Direct access not permitted');
}

class AdminAuth {
    private $db;
    private $security;

    public function __construct($db, $security) {
        $this->db = $db;
        $this->security = $security;
    }

    /**
     * Authenticate admin user
     */
    public function login($username, $password) {
        try {
            // Check if IP is blocked
            $clientIP = filter_var($_SERVER['REMOTE_ADDR'], FILTER_VALIDATE_IP);
            if ($this->security->isIPBlocked($clientIP)) {
                throw new Exception("Your IP address has been blocked due to multiple failed attempts. Please try again later.");
            }

            // Check rate limiting
            if (!$this->security->checkRateLimit($clientIP)) {
                throw new Exception("Too many attempts. Please try again later.");
            }

            // Get admin user from admins table by username
            $admin = $this->db->queryOne(
                "SELECT admin_id, username, password, first_name, last_name, email, role, status FROM admins WHERE username = ? AND status = 'active'",
                [$username]
            );

            if (!$admin) {
                // Log failed login attempt
                $this->security->logLoginAttempt($clientIP, $username, false);
                return false;
            }

            // Verify password
            if (!password_verify($password, $admin['password'])) {
                // Log failed login attempt
                $this->security->logLoginAttempt($clientIP, $username, false);
                return false;
            }

            // Update last login
            $this->db->query("UPDATE admins SET last_login = NOW() WHERE admin_id = ?", [$admin['admin_id']]);

            // Log successful login
            $this->security->logLoginAttempt($clientIP, $username, true);

            // Set session variables
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_id'] = $admin['admin_id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_name'] = $admin['first_name'] . ' ' . $admin['last_name'];
            $_SESSION['admin_email'] = $admin['email'];
            $_SESSION['admin_role'] = $admin['role'];
            $_SESSION['last_activity'] = time();

            // Add for dashboard.php compatibility
            $_SESSION['aid'] = $admin['admin_id'];

            // Set user type based on role
            if ($admin['role'] === 'subadmin') {
                $_SESSION['utype'] = 0; // Sub-admin
            } else {
                $_SESSION['utype'] = 1; // Main admin
            }

            // Generate new CSRF token
            $_SESSION[CSRF_TOKEN_NAME] = $this->generateCSRFToken();

            return true;
        } catch (Exception $e) {
            error_log("Admin Login Error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if admin is logged in
     */
    public function isLoggedIn() {
        if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
            return false;
        }

        // Check session timeout
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > ADMIN_SESSION_TIMEOUT) {
            $this->logout();
            return false;
        }

        // Update last activity
        $_SESSION['last_activity'] = time();
        return true;
    }

    /**
     * Logout admin user
     */
    public function logout() {
        if (isset($_SESSION['admin_id'])) {
            $this->security->logActivity($_SESSION['admin_id'], 'logout', 'Admin logged out');
        }
        
        // Clear all session data
        $_SESSION = array();
        
        // Destroy session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Destroy session
        session_destroy();
        return true;
    }

    /**
     * Get current admin info
     */
    public function getCurrentAdmin() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        return [
            'admin_id' => $_SESSION['admin_id'],
            'username' => $_SESSION['admin_username'],
            'name' => $_SESSION['admin_name'],
            'email' => $_SESSION['admin_email'],
            'role' => $_SESSION['admin_role']
        ];
    }

    /**
     * Check if admin has specific role
     */
    public function hasRole($role) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        return $_SESSION['admin_role'] === $role;
    }

    /**
     * Check if admin is super admin
     */
    public function isSuperAdmin() {
        return $this->hasRole('admin');
    }

    /**
     * Generate CSRF token
     */
    private function generateCSRFToken() {
        return bin2hex(random_bytes(32));
    }

    /**
     * Verify CSRF token
     */
    public function verifyCSRFToken($token) {
        return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
    }

    /**
     * Require admin authentication
     */
    public function requireAuth() {
        if (!$this->isLoggedIn()) {
            header('Location: ' . ADMIN_BASE_URL . 'pages/login.php');
            exit;
        }
    }

    /**
     * Require specific role
     */
    public function requireRole($role) {
        $this->requireAuth();
        
        if (!$this->hasRole($role)) {
            header('Location: ' . ADMIN_BASE_URL . 'pages/access-denied.php');
            exit;
        }
    }
}
