/**
 * Ad<PERSON>gin Styles
 * 
 * Styles for the admin login page
 */

body {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    font-family: 'Roboto', sans-serif;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    padding: 0;
    margin: 0;
}

/* Logo container */
.logo-container {
    position: absolute;
    top: 20px;
    left: 0;
    width: 100%;
    height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Logo at the top of the page */
.logo-container::before {
    content: "";
    width: 150px;
    height: 150px;
    background-image: url('/Online Booking Reservation System/website/assets/images/carleslogomunicipality.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.9;
}

/* Municipality text styling */
.municipality-text {
    user-select: none; /* Prevent text selection */
    pointer-events: none; /* Prevent clicking/interaction */
}



.login-container {
    background: rgba(255, 255, 255, 0.97);
    padding: 2.5rem 3rem;
    border-radius: 1.2rem;
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.25);
    width: 90%;
    max-width: 450px;
    border: none;
    position: relative;
    z-index: 1;
    margin-top: 200px;
}

.login-container h2 {
    color: #1e3a8a;
    margin-bottom: 1.2rem;
    text-align: center;
    letter-spacing: 1px;
    font-size: 1.8rem;
}

.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    color: #374151;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 1.1rem;
}

.input-group input {
    box-sizing: border-box;
    width: 100%;
    padding: 1rem 1.2rem;
    border: 1.5px solid #cbd5e1;
    border-radius: 0.9rem;
    font-size: 1.2rem;
    background: #f5f7fa;
    transition: border 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px rgba(31,38,135,0.07);
    color: #22223b;
    font-family: 'Roboto', sans-serif;
    letter-spacing: 0.02em;
}

.input-group input:focus {
    border-color: #6366f1;
    outline: none;
    box-shadow: 0 0 0 3px #a5b4fc44;
    background: #fff;
}

.login-btn {
    width: 100%;
    background: linear-gradient(90deg, #6366f1 0%, #1e3a8a 100%);
    color: #fff;
    border: none;
    padding: 1rem;
    border-radius: 0.8rem;
    font-size: 1.3rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px 0 rgba(99,102,241,0.08);
    margin-top: 1rem;
}

.login-btn:hover {
    background: linear-gradient(90deg, #1e3a8a 0%, #6366f1 100%);
    box-shadow: 0 4px 16px 0 rgba(99,102,241,0.16);
}

.error-msg {
    color: #dc2626;
    margin-bottom: 1rem;
    text-align: center;
    background: #fee2e2;
    border-radius: 0.5rem;
    padding: 0.5rem 0.7rem;
    border: 1px solid #fecaca;
}

.success-msg {
    color: #16a34a;
    margin-bottom: 1rem;
    text-align: center;
    background: #dcfce7;
    border-radius: 0.5rem;
    padding: 0.5rem 0.7rem;
    border: 1px solid #bbf7d0;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 1.2rem;
}

.logo img {
    height: 64px;
    border-radius: 12px;
    box-shadow: 0 2px 8px 0 rgba(99,102,241,0.10);
}

.password-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.password-wrapper input[type="password"],
.password-wrapper input[type="text"] {
    width: 100%;
}

.toggle-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #a1a1aa;
    font-size: 1.15rem;
    transition: color 0.2s;
    z-index: 2;
}

.toggle-password:hover {
    color: #6366f1;
}

.admin-notice {
    text-align: center;
    margin-top: 1.5rem;
    padding: 0.75rem;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 0.5rem;
    color: #92400e;
    font-size: 0.9rem;
}

.footer {
    width: 100%;
    text-align: center;
    padding: 0.5rem;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 0.05em;
    background: rgba(0, 0, 0, 0.2);
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 10;
    user-select: none;
    font-weight: 400;
}
