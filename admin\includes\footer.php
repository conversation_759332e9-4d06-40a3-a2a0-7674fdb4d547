  <footer class="main-footer">
  <div class="float-right d-none d-sm-inline-block">
    <b>Balangay Boat Tours</b> | <b>Sailing Through History and Nature</b>
  </div>
  <div style="color:#aaa;">
    &copy; <?php echo date('Y'); ?> Balangay Boat Tours. All rights reserved.
  </div>
</footer>

<!-- Session Timeout Warning Script -->
<script>
// Session timeout configuration
const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes in milliseconds
const WARNING_TIME = 5 * 60 * 1000; // 5 minutes warning
let sessionTimer;
let warningTimer;
let sessionWarningShown = false;

// Initialize session timeout
function initSessionTimeout() {
    // Clear existing timers
    if (sessionTimer) clearTimeout(sessionTimer);
    if (warningTimer) clearTimeout(warningTimer);

    sessionWarningShown = false;

    // Set warning timer (25 minutes)
    warningTimer = setTimeout(() => {
        if (!sessionWarningShown) {
            sessionWarningShown = true;
            showSessionWarning();
        }
    }, SESSION_TIMEOUT - WARNING_TIME);

    // Set logout timer (30 minutes)
    sessionTimer = setTimeout(() => {
        // Auto logout
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Session Expired',
                text: 'Your session has expired. You will be redirected to the login page.',
                icon: 'error',
                timer: 3000,
                showConfirmButton: false,
                allowOutsideClick: false
            }).then(() => {
                window.location.href = 'logout.php';
            });
        } else {
            alert('Session expired. You will be redirected to login.');
            window.location.href = 'logout.php';
        }
    }, SESSION_TIMEOUT);
}

// Show session warning
function showSessionWarning() {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'Session Expiring Soon!',
            text: 'Your session will expire in 5 minutes. Do you want to extend your session?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Extend Session',
            cancelButtonText: 'Continue Working',
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            allowOutsideClick: false,
            timer: 60000, // Auto-close after 1 minute if no response
            timerProgressBar: true
        }).then((result) => {
            if (result.isConfirmed) {
                extendSession();
            } else if (result.dismiss === Swal.DismissReason.timer) {
                showFinalWarning();
            }
        });
    } else {
        // Fallback if SweetAlert is not available
        if (confirm('Your session will expire in 5 minutes. Click OK to extend your session.')) {
            extendSession();
        }
    }
}

// Show final warning (last minute)
function showFinalWarning() {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'Final Warning!',
            text: 'Your session will expire in 1 minute. Click OK to stay logged in.',
            icon: 'error',
            confirmButtonText: 'Stay Logged In',
            confirmButtonColor: '#dc3545',
            allowOutsideClick: false,
            timer: 30000, // 30 seconds
            timerProgressBar: true
        }).then((result) => {
            if (result.isConfirmed) {
                extendSession();
            }
        });
    } else {
        if (confirm('Final warning! Your session will expire in 1 minute. Click OK to stay logged in.')) {
            extendSession();
        }
    }
}

// Extend session via AJAX
function extendSession() {
    fetch('extend_session.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({action: 'extend'})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reset timers
            initSessionTimeout();

            // Show success message
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'Session Extended!',
                    text: 'Your session has been extended for another 30 minutes.',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        } else {
            window.location.href = 'logout.php';
        }
    })
    .catch(error => {
        console.error('Error extending session:', error);
        window.location.href = 'logout.php';
    });
}

// Reset session timer on user activity
function resetSessionTimer() {
    if (sessionWarningShown) {
        sessionWarningShown = false;
        initSessionTimeout();
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initSessionTimeout();

    // Reset timer on user activity
    ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
        document.addEventListener(event, resetSessionTimer, { passive: true });
    });
});
</script>

<script src="plugins/jquery/jquery.min.js"></script>
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="dist/js/adminlte.min.js"></script>
<script src="js/notification-fix.js"></script>
<!-- Note: demo.js has been disabled for production use -->

<script>
(function(){
  var btn = document.querySelector('[data-widget="pushmenu"]');
  var body = document.body;

  // Always start with sidebar open
  body.classList.remove('sidebar-collapse');
  localStorage.removeItem('sidebar-collapsed');

  if(btn) {
    btn.addEventListener('click', function(e){
      e.preventDefault();
      body.classList.toggle('sidebar-collapse');
      // Optionally, you can save state here if you want persistence
    });
  }
})();
</script>