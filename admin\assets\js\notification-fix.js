/**
 * Notification Modal Fix
 * 
 * This script fixes the issue with the modal backdrop remaining
 * after closing the notification modal.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Fix for modal backdrop not being removed
    $(document).on('hidden.bs.modal', '#notifModal, #fullNotifModal', function() {
        // Check if there are no other modals open
        if ($('.modal.show').length === 0) {
            // Remove any lingering backdrops
            $('.modal-backdrop').remove();
            
            // Remove modal-open class from body
            $('body').removeClass('modal-open');
            
            // Remove inline styles added by Bootstrap
            $('body').css('padding-right', '');
            $('body').css('overflow', '');
        }
    });
    
    // Additional fix for when one modal is closed to show another
    $(document).on('hide.bs.modal', '#notifModal', function() {
        // If we're opening the full notification modal, ensure clean transition
        if ($('#fullNotifModal').hasClass('show') || $('#fullNotifModal').hasClass('in')) {
            setTimeout(function() {
                // Remove any duplicate backdrops
                if ($('.modal-backdrop').length > 1) {
                    $('.modal-backdrop').slice(1).remove();
                }
            }, 100);
        }
    });
    
    // Same for the full notification modal
    $(document).on('hide.bs.modal', '#fullNotifModal', function() {
        // If we're opening the notification modal, ensure clean transition
        if ($('#notifModal').hasClass('show') || $('#notifModal').hasClass('in')) {
            setTimeout(function() {
                // Remove any duplicate backdrops
                if ($('.modal-backdrop').length > 1) {
                    $('.modal-backdrop').slice(1).remove();
                }
            }, 100);
        }
    });
    
    console.log('Notification modal fix loaded');
});
